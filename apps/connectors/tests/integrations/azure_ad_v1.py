import base64
import json
from datetime import datetime
from unittest.mock import patch

import responses
from microsoft_client.exceptions import OAuthHTTPError
from pytz import utc
from responses import matchers

from apps.connectors.health_checks.components.component import (
    HealthCheckComponent,
    HealthCheckRequirement,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.external_user_profile_link import (
    ExternalUserProfileLinkResult,
)
from apps.connectors.integrations.actions.user.get_sign_in_logs import (
    SignInLogsByIpArgs,
    SignInLogsByUPNArgs,
    SignInLogsByUserIdArgs,
    SignInLogsResult,
)
from apps.connectors.integrations.actions.user.reset_user_password import (
    ResetUserPasswordStatus,
)
from apps.connectors.integrations.actions.user.revoke_user_sessions import (
    RevokeUserSessionsStatus,
)
from apps.connectors.integrations.actions.user.risky_user import (
    RiskyUserState,
)
from apps.connectors.integrations.actions.user.user_login import UserLoginStatus
from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.schemas import (
    UPNIdentifier,
    UPNIdentifierArgs,
    UserIdentifier,
    UserIdentifierArgs,
)
from apps.connectors.integrations.schemas.identifiers import IpAddressIdentifier
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    ConnectionHealthCheck,
    ReadAll,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
    convert_alert_to_ocsf,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.connectors.tests.integrations.ms_xdr_v1 import (
    get_ms_xdr_events_artifact_lines,
)
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories import AadAppFactory, ConnectorFactory


def format_token(access_token):
    # Format as JWT token
    access_token_bytes = json.dumps(access_token).encode("utf-8")
    access_token_base64 = base64.b64encode(access_token_bytes).decode()
    # no need to set up a proper header and signature, since they are not verified
    # by the library
    return f"header.{access_token_base64}.signature"


def setup_oauth_responses(tenant_id, roles: list = None):
    if roles is None:
        roles = [
            "SecurityAlert.ReadWrite.All",
            "Directory.Read.All",
            "User.Read.All",
            "User.ManageIdentities.All",
            "User.RevokeSessions.All",
            "IdentityRiskyUser.ReadWrite.All",
            "AuditLog.Read.All",
        ]
    access_token = {
        "aud": "https://graph.microsoft.com",
        "aio": "E2FgYNCtS2B6fdLpYGHq4sKWlEnmAA==",
        "app_displayname": "Critical Start MDR - Azure Sentinel",
        "roles": roles,
    }
    responses.post(
        f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token",
        json={
            "access_token": format_token(access_token),
            "expires_in": 3600,
            "token_type": "Bearer",
        },
    )


def setup_host_sync_responses(tenant_id, roles: list = None):
    setup_oauth_responses(tenant_id, roles)

    params = {}
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "displayName": "test_device_name_1",
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_2",
                    "displayName": "test_device_name_2",
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_3",
                    "displayName": "test_device_name_3",
                    "trustType": "ServerAd",
                },
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$select": "id",
        "$expand": "registeredOwners($select=id, displayName, mail)",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "registeredOwners": [
                        {
                            "@odata.type": "#microsoft.graph.user",
                            "id": "owner_id_1",
                            "displayName": "Owner First",
                        }
                    ],
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_2",
                    "registeredOwners": [
                        {
                            "@odata.type": "#microsoft.graph.user",
                            "id": "owner_id_2",
                            "displayName": "Owner Second",
                        }
                    ],
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_3",
                    "registeredOwners": [
                        {
                            "@odata.type": "#microsoft.graph.user",
                            "id": "owner_id_3",
                            "displayName": "Owner Third",
                        }
                    ],
                    "trustType": "ServerAd",
                },
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$expand": "memberOf",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "displayName": "test_device_name_1",
                    "memberOf": [
                        {"@odata.type": "#microsoft.graph.group", "id": "group_id_1"}
                    ],
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_2",
                    "displayName": "test_device_name_2",
                    "memberOf": [
                        {"@odata.type": "#microsoft.graph.group", "id": "group_id_1"}
                    ],
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_3",
                    "displayName": "test_device_name_3",
                    "memberOf": [
                        {"@odata.type": "#microsoft.graph.group", "id": "group_id_2"}
                    ],
                    "trustType": "ServerAd",
                },
            ],
        },
    )


def get_device_response_obj(id, group_number=1, trust_type="ServerAd"):
    return {
        "id": "azure_ad_" + str(id),
        "displayName": "test_device_name_" + str(id),
        "deviceId": "d2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2",
        "domainName": "WORKGROUP",
        "operatingSystem": "macOS",
        "operatingSystemVersion": "13.4.1 (22F82)",
        "approximateLastSignInDateTime": "2022-10-30T19:02:02Z",
        "memberOf": [
            {
                "@odata.type": "#microsoft.graph.group",
                "id": "group_id_" + str(group_number),
                "displayName": "Group " + str(group_number),
            }
        ],
        "trustType": trust_type,
    }


def get_empty_device_response_obj(id):
    return {
        "id": "azure_ad_" + str(id),
        "displayName": "",
        "domainName": None,
        "operatingSystem": None,
        "operatingSystemVersion": None,
        "approximateLastSignInDateTime": None,
        "memberOf": [],
        "trustType": "ServerAd",
    }


def get_device_owners_response_obj(
    id, create_email=True, with_local=False, trust_type="ServerAd"
):
    return {
        "id": "azure_ad_" + str(id),
        "registeredOwners": [
            {
                "@odata.type": "#microsoft.graph.user",
                "id": "owner_id_" + str(id),
                "displayName": "Owner " + str(id),
                "mail": "owner"
                + str(id)
                + ("@test.com" if not with_local else "@test.local")
                if create_email
                else None,
            }
        ],
        "trustType": trust_type,
    }


def get_comparison_object(id, create_email=True, group_number=1, with_local=False):
    return {
        "source_id": "azure_ad_" + str(id),
        "group_names": ["Group " + str(group_number)],
        "hostname": "test_device_name_" + str(id),
        "fqdns": ["test_device_name_" + str(id) + ".workgroup"],
        "ip_addresses": [],
        "mac_addresses": [],
        "is_internet_facing": None,
        "internet_exposure": "unknown",
        "os": {
            "family": "mac",
            "name": "macOS 13.4.1 (22F82)",
            "host_type": "workstation",
        },
        "owners": [
            {
                "email": "owner" + str(id) + "@test.com"
                if create_email and not with_local
                else None,
                "name": "Owner " + str(id),
            }
        ],
        "aad_id": "d2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2",
        "criticality": "unknown",
        "last_seen": "2022-10-30T19:02:02Z",
        "source_data": {
            **get_device_response_obj(id, group_number=group_number),
            **get_device_owners_response_obj(
                id, create_email=create_email, with_local=with_local
            ),
        },
    }


def get_empty_comparison_object(id):
    return {
        "source_id": "azure_ad_" + str(id),
        "group_names": [],
        "hostname": "",
        "fqdns": [],
        "ip_addresses": [],
        "mac_addresses": [],
        "is_internet_facing": None,
        "internet_exposure": "unknown",
        "os": {
            "family": "unknown",
            "name": None,
            "host_type": "unknown",
        },
        "owners": [],
        "aad_id": None,
        "criticality": "unknown",
        "last_seen": None,
        "source_data": {
            **get_empty_device_response_obj(id),
            "registeredOwners": [],
        },
    }


def setup_host_sync_for_normalization(tenant_id, roles: list = None, with_local=False):
    setup_oauth_responses(tenant_id, roles)

    params = {}
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "displayName": "test_device_name_1",
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_2",
                    "displayName": "test_device_name_2",
                    "trustType": "ServerAd",
                },
                {
                    "id": "azure_ad_3",
                    "displayName": "test_device_name_3",
                    "trustType": "ServerAd",
                },
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$select": "id",
        "$expand": "registeredOwners($select=id, displayName, mail)",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                get_device_owners_response_obj(1, with_local=with_local),
                get_device_owners_response_obj(2, with_local=with_local),
                get_device_owners_response_obj(3, with_local=with_local),
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$expand": "memberOf",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                get_device_response_obj(1),
                get_device_response_obj(2, group_number=2),
                get_device_response_obj(3),
            ],
        },
    )


def setup_host_sync_normalized_empty_responses(tenant_id, roles: list = None):
    setup_oauth_responses(tenant_id, roles)

    params = {}
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "displayName": "test_device_name_1",
                },
                {
                    "id": "azure_ad_2",
                    "displayName": "test_device_name_2",
                },
                {
                    "id": "azure_ad_3",
                    "displayName": "test_device_name_3",
                },
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$select": "id",
        "$expand": "registeredOwners($select=id, displayName, mail)",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "registeredOwners": [],
                },
                {
                    "id": "azure_ad_2",
                    "registeredOwners": [],
                },
                {
                    "id": "azure_ad_3",
                    "registeredOwners": [],
                },
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$expand": "memberOf",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                get_empty_device_response_obj(1),
                get_empty_device_response_obj(2),
                get_empty_device_response_obj(3),
            ],
        },
    )


def setup_host_sync_normalized_responses_owner_without_email(
    tenant_id, roles: list = None
):
    setup_oauth_responses(tenant_id, roles)

    params = {}
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "displayName": "test_device_name_1",
                },
                {
                    "id": "azure_ad_2",
                    "displayName": "test_device_name_2",
                },
                {
                    "id": "azure_ad_3",
                    "displayName": "test_device_name_3",
                },
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$select": "id",
        "$expand": "registeredOwners($select=id, displayName, mail)",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                get_device_owners_response_obj(1, create_email=False),
                get_device_owners_response_obj(2, create_email=False),
                get_device_owners_response_obj(3, create_email=False),
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$expand": "memberOf",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                get_device_response_obj(1),
                get_device_response_obj(2),
                get_device_response_obj(3),
            ],
        },
    )


def setup_host_sync_normalized_responses_owner_for_entra(tenant_id, roles: list = None):
    setup_oauth_responses(tenant_id, roles)

    params = {}
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                {
                    "id": "azure_ad_1",
                    "displayName": "test_device_name_1",
                },
                {
                    "id": "azure_ad_2",
                    "displayName": "test_device_name_2",
                },
                {
                    "id": "azure_ad_3",
                    "displayName": "test_device_name_3",
                },
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$select": "id",
        "$expand": "registeredOwners($select=id, displayName, mail)",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                get_device_owners_response_obj(1, create_email=False),
                get_device_owners_response_obj(2, create_email=False),
                get_device_owners_response_obj(
                    3, create_email=False, trust_type="AzureAd"
                ),
            ],
        },
    )

    params = {
        "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
        "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
        "displayName, 'test')",
        "$expand": "memberOf",
    }
    responses.get(
        "https://graph.microsoft.com/v1.0/devices",
        match=[matchers.query_param_matcher(params)],
        json={
            "value": [
                get_device_response_obj(1),
                get_device_response_obj(2),
                get_device_response_obj(3, trust_type="AzureAd"),
            ],
        },
    )


def setup_user_action_responses(tenant_id, user_id, upn=None):
    setup_oauth_responses(tenant_id)
    upn = upn or "test_upn"

    # Get user
    result = {
        "id": user_id,
        "accountEnabled": True,
        "displayName": "Test User",
        "userPrincipalName": upn or "test_upn",
    }
    responses.get(
        f"https://graph.microsoft.com/v1.0/users/{user_id}",
        json=result,
    )
    responses.get(
        f"https://graph.microsoft.com/v1.0/users/{upn}",
        json=result,
    )

    # Disable user login
    responses.patch(
        f"https://graph.microsoft.com/v1.0/users/{user_id}",
        match=[responses.matchers.json_params_matcher({"accountEnabled": False})],
        status=204,
    )
    responses.patch(
        f"https://graph.microsoft.com/v1.0/users/{upn}",
        match=[responses.matchers.json_params_matcher({"accountEnabled": False})],
        status=204,
    )

    # Enable user login
    responses.patch(
        f"https://graph.microsoft.com/v1.0/users/{user_id}",
        match=[responses.matchers.json_params_matcher({"accountEnabled": True})],
        status=204,
    )
    responses.patch(
        f"https://graph.microsoft.com/v1.0/users/{upn}",
        match=[responses.matchers.json_params_matcher({"accountEnabled": True})],
        status=204,
    )

    # Reset user password
    for user_identifier in (user_id, upn):
        responses.patch(
            f"https://graph.microsoft.com/v1.0/users/{user_identifier}",
            match=[
                responses.matchers.json_params_matcher(
                    {
                        "passwordProfile": {
                            "forceChangePasswordNextSignInWithMfa": True,
                        }
                    }
                )
            ],
            status=204,
        )

    # Revoke user sessions
    responses.post(
        f"https://graph.microsoft.com/v1.0/users/{user_id}/revokeSignInSessions",
        json={"value": True},
        status=200,
    )
    responses.post(
        f"https://graph.microsoft.com/v1.0/users/{upn}/revokeSignInSessions",
        json={"value": True},
        status=200,
    )

    # Confirm risky user
    responses.post(
        f"https://graph.microsoft.com/v1.0/identityProtection/riskyUsers/confirmCompromised",
        json={"userIds": [user_id]},
    )

    # Dismiss risky user
    responses.post(
        f"https://graph.microsoft.com/v1.0/identityProtection/riskyUsers/dismiss",
        json={"userIds": [user_id]},
    )


def setup_audit_log_action_responses(tenant_id, params=None):
    setup_oauth_responses(tenant_id)

    # Get sign-in logs
    responses.get(
        f"https://graph.microsoft.com/beta/auditLogs/signIns?{params if params else ''}",
        json={
            "value": [
                {
                    "id": "66ea54eb-6301-4ee5-be62-ff5a759b0100",
                    "createdDateTime": "2023-12-01T16:03:35Z",
                    "userDisplayName": "Test Entra User",
                    "userPrincipalName": "<EMAIL>",
                    "userId": "b45bb39e-d7da-4bc5-b8e7-9202b16882bd",
                    "appId": "9bf03e1f-7082-46e7-962a-61a954e6a4d2",
                    "appDisplayName": "Test app explorer",
                    "ipAddress": "**************",
                    "clientAppUsed": "Browser",
                    "correlationId": "87e8cb6e-9862-4ca6-a797-e747c3ac5ed6",
                    "conditionalAccessStatus": "notApplied",
                    "isInteractive": True,
                    "riskDetail": "none",
                    "riskLevelAggregated": "none",
                    "riskLevelDuringSignIn": "none",
                    "riskState": "none",
                    "riskEventTypes": [],
                    "resourceDisplayName": "Microsoft Graph",
                    "resourceId": "00000003-0000-0000-c000-000000000000",
                    "status": {
                        "errorCode": 0,
                        "failureReason": None,
                        "additionalDetails": None,
                    },
                    "deviceDetail": {
                        "deviceId": "d2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2",
                        "displayName": "DevLab01",
                        "operatingSystem": "Windows 3.1",
                        "browser": "Edge 80.0.361",
                        "isCompliant": None,
                        "isManaged": None,
                        "trustType": None,
                    },
                    "location": {
                        "city": "Redmond",
                        "state": "Washington",
                        "countryOrRegion": "US",
                        "geoCoordinates": {
                            "altitude": None,
                            "latitude": 47.68050003051758,
                            "longitude": -122.12094116210938,
                        },
                    },
                    "appliedConditionalAccessPolicies": [
                        {
                            "id": "23e95c9f-7b73-478e-8fb9-f0e7ae90d68b",
                            "displayName": "SharePoint limited access for guest workers",
                            "enforcedGrantControls": [],
                            "enforcedSessionControls": [],
                            "result": "notEnabled",
                        },
                        {
                            "id": "fd803ba6-c38c-4843-8110-423806a5711b",
                            "displayName": "Medium signin risk block",
                            "enforcedGrantControls": [],
                            "enforcedSessionControls": [],
                            "result": "Enabled",
                        },
                    ],
                    "authenticationDetails": [
                        {
                            "authenticationStepDateTime": "2025-06-23T14:35:11Z",
                            "authenticationMethod": "Password",
                            "authenticationMethodDetail": "Password in the cloud",
                            "succeeded": True,
                            "authenticationStepResultDetail": "Correct password",
                            "authenticationStepRequirement": "",
                        },
                        {
                            "authenticationStepDateTime": "2025-06-23T14:32:34Z",
                            "authenticationMethod": "Previously satisfied",
                            "authenticationMethodDetail": None,
                            "succeeded": True,
                            "authenticationStepResultDetail": "First factor requirement satisfied by claim in the token",
                            "authenticationStepRequirement": "",
                        },
                        {
                            "authenticationStepDateTime": "2025-06-23T14:35:12Z",
                            "authenticationMethod": "SMS",
                            "authenticationMethodDetail": "Text message to +1234567890",
                            "succeeded": True,
                            "authenticationStepResultDetail": "User provided correct security code",
                            "authenticationStepRequirement": "multiFactorAuthentication",
                        },
                        {
                            "authenticationStepDateTime": "2025-06-23T14:35:13Z",
                            "authenticationMethod": "Voice",
                            "authenticationMethodDetail": "Phone call to +1234567890",
                            "succeeded": True,
                            "authenticationStepResultDetail": "User responded to phone call",
                            "authenticationStepRequirement": "multiFactorAuthentication",
                        },
                        {
                            "authenticationStepDateTime": "2025-06-23T14:35:14Z",
                            "authenticationMethod": "Authenticator App",
                            "authenticationMethodDetail": "Microsoft Authenticator notification",
                            "succeeded": True,
                            "authenticationStepResultDetail": "User approved the authentication",
                            "authenticationStepRequirement": "multiFactorAuthentication",
                        },
                        {
                            "authenticationStepDateTime": "2025-06-23T14:35:15Z",
                            "authenticationMethod": "Software OATH token",
                            "authenticationMethodDetail": "OATH verification code",
                            "succeeded": True,
                            "authenticationStepResultDetail": "User provided correct OATH code",
                            "authenticationStepRequirement": "multiFactorAuthentication",
                        },
                        {
                            "authenticationStepDateTime": "2025-06-23T14:35:16Z",
                            "authenticationMethod": "Satisfied by token",
                            "authenticationMethodDetail": "Token-based authentication",
                            "succeeded": True,
                            "authenticationStepResultDetail": "Authentication satisfied by token",
                            "authenticationStepRequirement": "multiFactorAuthentication",
                        },
                        {
                            "authenticationStepDateTime": "2025-06-23T14:35:18Z",
                            "authenticationMethod": "Unknown Method",
                            "authenticationMethodDetail": "Some unknown authentication method",
                            "succeeded": False,
                            "authenticationStepResultDetail": "Unknown authentication method failed",
                            "authenticationStepRequirement": "multiFactorAuthentication",
                        },
                    ],
                }
            ]
        },
    )


class AzureAdV1ApiTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()
        aad_app = AadAppFactory()
        integration = ConnectorFactory.get_integration(
            technology_id="azure_ad", config__client_id=aad_app.client_id
        )
        setup_host_sync_responses(integration.config["tenant_id"])
        self.upn = "test_upn"
        self.user_id = "test_user_id"
        setup_user_action_responses(
            integration.config["tenant_id"], self.user_id, self.upn
        )
        self.api = integration.get_api()

    @responses.activate
    def test_get_devices(self):
        devices = self.api.get_devices()
        self.assertEqual(
            devices,
            {
                "value": [
                    {
                        "id": "azure_ad_1",
                        "displayName": "test_device_name_1",
                        "trustType": "ServerAd",
                    },
                    {
                        "id": "azure_ad_2",
                        "displayName": "test_device_name_2",
                        "trustType": "ServerAd",
                    },
                    {
                        "id": "azure_ad_3",
                        "displayName": "test_device_name_3",
                        "trustType": "ServerAd",
                    },
                ],
            },
        )

    @responses.activate
    def test_has_permission(self):
        self.assertTrue(self.api.has_permission("Directory.Read.All"))
        self.assertFalse(self.api.has_permission("Application.Read.All"))

    @responses.activate
    def test_get_user(self):
        user_info = self.api.get_user(user_id=self.user_id)
        self.assertTrue(
            user_info,
            {
                "id": self.user_id,
                "accountEnabled": True,
                "displayName": "Test User",
                "userPrincipalName": self.upn,
            },
        )

    @responses.activate
    def test_update_user(self):
        response = self.api.update_user(self.user_id, {"accountEnabled": True})
        self.assertEqual(response, "")

    @responses.activate
    def test_get_risky_user(self):
        # Existing state of the user is compromised
        responses.get(
            f"https://graph.microsoft.com/v1.0/identityProtection/riskyUsers/{self.user_id}",
            json={
                "riskState": "confirmedCompromised",
                "isProcessing": True,
            },
        )
        response = self.api.get_risky_user(self.user_id)
        self.assertEqual(
            response,
            {
                "riskState": "confirmedCompromised",
                "isProcessing": True,
            },
        )


class AzureAdV1IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        super().setUp()
        mock_date = self.patch("django.utils.timezone.now")
        mock_date.return_value = datetime(2023, 9, 1, 23, tzinfo=utc)

    @staticmethod
    def default_settings():
        return {
            "host_sync": {
                "fetch_from_last_x_days": 20,
                "fetch_disabled_devices": False,
                "fetch_entra_joined_devices": False,
                "include_device_owner": True,
                "include_device_groups": True,
                "fetch_by_groups": "group_id_1,group_id_99",
                "custom_filter_expression": "(startsWith(displayName, 'test')",
            }
        }

    def setup_integration(self, settings=None):
        if settings is None:
            settings = self.settings

        aad_app = AadAppFactory()
        self.integration = ConnectorFactory.get_integration(
            technology_id="azure_ad",
            config__client_id=aad_app.client_id,
            settings=settings,
        )
        self.tenant_id = self.integration.config["tenant_id"]
        self.user_id = "test_user_id"
        self.upn = "<EMAIL>"
        return self.integration

    @responses.activate
    def test_get_hosts_with_local_in_email(self):
        settings = self.settings
        aad_app = AadAppFactory()
        self.integration = ConnectorFactory.get_integration(
            technology_id="azure_ad",
            config__client_id=aad_app.client_id,
            settings=settings,
        )
        self.tenant_id = self.integration.config["tenant_id"]
        self.user_id = "test_user_id"
        self.upn = "<EMAIL>"

        setup_host_sync_for_normalization(self.tenant_id, with_local=True)
        result = self.integration.invoke_action(IntegrationActionType.HOST_SYNC)
        self.assertEqual(
            list(serialize(result)),
            [
                get_comparison_object(1, with_local=True),
                get_comparison_object(3, with_local=True),
            ],
        )

    @responses.activate
    def test_get_hosts_with_local_invalid_email(self):
        settings = self.settings
        aad_app = AadAppFactory()
        self.integration = ConnectorFactory.get_integration(
            technology_id="azure_ad",
            config__client_id=aad_app.client_id,
            settings=settings,
        )
        self.tenant_id = self.integration.config["tenant_id"]

        setup_oauth_responses(self.tenant_id)
        params = {
            "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
            "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
            "displayName, 'test')",
            "$select": "id",
            "$expand": "registeredOwners($select=id, displayName, mail)",
        }
        responses.get(
            "https://graph.microsoft.com/v1.0/devices",
            match=[matchers.query_param_matcher(params)],
            json={
                "value": [
                    {
                        "id": "azure_ad_1",
                        "registeredOwners": [
                            {
                                "@odata.type": "#microsoft.graph.user",
                                "id": "owner_id_1",
                                "displayName": "Owner 1",
                                "mail": "owner1com",
                            }
                        ],
                    },
                    {
                        "id": "azure_ad_3",
                        "registeredOwners": [
                            {
                                "@odata.type": "#microsoft.graph.user",
                                "id": "owner_id_3",
                                "displayName": "Owner 3",
                                "mail": "<EMAIL>",
                            }
                        ],
                    },
                ],
            },
        )

        params = {
            "$filter": "(onPremisesLastSyncDateTime ge 2023-08-12T23:00:00+00:00 OR approximateLastSignInDateTime ge "
            "2023-08-12T23:00:00+00:00) AND accountEnabled eq true AND (startsWith("
            "displayName, 'test')",
            "$expand": "memberOf",
        }
        responses.get(
            "https://graph.microsoft.com/v1.0/devices",
            match=[matchers.query_param_matcher(params)],
            json={
                "value": [
                    get_device_response_obj(1),  # has invalid email, fails to normalize
                    get_device_response_obj(2, group_number=2),  # filtered out by group
                    get_device_response_obj(3),  # has valid email
                ],
            },
        )

        result = self.integration.invoke_action(IntegrationActionType.HOST_SYNC)

        comparison_one = get_comparison_object(1, create_email=False)
        comparison_one["source_data"]["registeredOwners"][0]["mail"] = "owner1com"
        self.assertEqual(
            list(serialize(result)),
            [
                comparison_one,
                get_comparison_object(3),
            ],
        )

    @responses.activate
    def test_get_hosts(self):
        self.setup_integration()
        setup_host_sync_for_normalization(self.tenant_id)

        self.assert_host_sync_results(
            [
                get_comparison_object(1),
                get_comparison_object(3),
            ]
        )

    @responses.activate
    def test_microsoft_ad_device_owner_without_email(self):
        self.setup_integration()
        setup_host_sync_normalized_responses_owner_without_email(self.tenant_id)

        self.assert_host_sync_results(
            [
                get_comparison_object(1, create_email=False),
                get_comparison_object(2, create_email=False),
                get_comparison_object(3, create_email=False),
            ]
        )

    @responses.activate
    def test_microsoft_ad_device_owner_with_entra(self):
        self.setup_integration()
        setup_host_sync_normalized_responses_owner_for_entra(self.tenant_id)

        self.assert_host_sync_results(
            [
                get_comparison_object(1, create_email=False),
                get_comparison_object(2, create_email=False),
            ]
        )

    @responses.activate
    def test_microsoft_ad_device_empty(self):
        self.settings["host_sync"]["fetch_by_groups"] = ""
        self.setup_integration()
        setup_host_sync_normalized_empty_responses(self.tenant_id)

        self.assert_host_sync_results(
            [
                get_empty_comparison_object(1),
                get_empty_comparison_object(2),
                get_empty_comparison_object(3),
            ]
        )

    @responses.activate
    def test_get_hosts_all_groups(self):
        self.settings["host_sync"]["fetch_by_groups"] = ""
        self.settings["host_sync"][
            "custom_filter_expression"
        ] = "startsWith(displayName, 'test'"
        self.setup_integration()
        setup_host_sync_for_normalization(self.tenant_id)

        self.assert_host_sync_results(
            [
                get_comparison_object(1),
                get_comparison_object(2, group_number=2),
                get_comparison_object(3),
            ]
        )

    @responses.activate
    def test_user_actions(self):
        self.setup_integration()
        setup_user_action_responses(self.tenant_id, self.user_id, self.upn)

        actions = [
            (
                IntegrationActionType.DISABLE_USER_LOGIN,
                UserIdentifierArgs(
                    user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
                ),
                UserLoginStatus,
                "enabled",
                False,
            ),
            (
                IntegrationActionType.DISABLE_USER_LOGIN_BY_UPN,
                UPNIdentifierArgs(
                    upn=UPNIdentifier(value_type="azure_ad", value=self.upn)
                ),
                UserLoginStatus,
                "enabled",
                False,
            ),
            (
                IntegrationActionType.ENABLE_USER_LOGIN,
                UserIdentifierArgs(
                    user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
                ),
                UserLoginStatus,
                "enabled",
                True,
            ),
            (
                IntegrationActionType.ENABLE_USER_LOGIN_BY_UPN,
                UPNIdentifierArgs(
                    upn=UPNIdentifier(value_type="azure_ad", value=self.upn)
                ),
                UserLoginStatus,
                "enabled",
                True,
            ),
            (
                IntegrationActionType.REVOKE_USER_SESSIONS,
                UserIdentifierArgs(
                    user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
                ),
                RevokeUserSessionsStatus,
                "revoked",
                True,
            ),
            (
                IntegrationActionType.REVOKE_USER_SESSIONS_BY_UPN,
                UPNIdentifierArgs(
                    upn=UPNIdentifier(value_type="azure_ad", value=self.upn)
                ),
                RevokeUserSessionsStatus,
                "revoked",
                True,
            ),
            (
                IntegrationActionType.RESET_USER_PASSWORD,
                UserIdentifierArgs(
                    user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
                ),
                ResetUserPasswordStatus,
                "reset",
                True,
            ),
            (
                IntegrationActionType.RESET_USER_PASSWORD_BY_UPN,
                UPNIdentifierArgs(
                    upn=UPNIdentifier(value_type="azure_ad", value=self.upn)
                ),
                ResetUserPasswordStatus,
                "reset",
                True,
            ),
        ]

        for action, action_args, action_result, attribute, expected_result in actions:
            result = self.integration.invoke_action(
                action,
                action_args,
            )

            self.assertTrue(isinstance(result.result, action_result))
            self.assertEqual(getattr(result.result, attribute), expected_result)

    @responses.activate
    def test_risky_user_action(self):
        self.setup_integration()
        setup_user_action_responses(self.tenant_id, self.user_id)

        actions = [
            IntegrationActionType.CONFIRM_RISKY_USER,
            IntegrationActionType.DISMISS_RISKY_USER,
        ]

        for action in actions:
            # Existing state of the user is None
            responses.get(
                f"https://graph.microsoft.com/v1.0/identityProtection/riskyUsers/{self.user_id}",
                json={"riskState": "", "isProcessing": False},
            )

            result = self.integration.invoke_action(
                action,
                UserIdentifierArgs(
                    user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
                ),
            )

            status = result.result
            self.assertTrue(status.is_processing)
            self.assertTrue(status.risk_state == None)

    @responses.activate
    def test_risky_user_action_with_upn(self):
        self.setup_integration()
        setup_user_action_responses(self.tenant_id, self.user_id)

        actions = [
            IntegrationActionType.CONFIRM_RISKY_USER_BY_UPN,
            IntegrationActionType.DISMISS_RISKY_USER_BY_UPN,
        ]

        for action in actions:
            # Existing state of the user is none
            responses.get(
                f"https://graph.microsoft.com/v1.0/identityProtection/riskyUsers/{self.user_id}",
                json={"riskState": "", "isProcessing": False},
            )

            responses.get(
                f"https://graph.microsoft.com/v1.0/users/{self.upn}",
                json={
                    "id": self.user_id,
                    "accountEnabled": False,
                    "displayName": "Test User",
                    "userPrincipalName": self.upn,
                },
            )

            result = self.integration.invoke_action(
                action,
                UPNIdentifierArgs(
                    upn=UPNIdentifier(value_type="azure_ad", value=self.upn),
                ),
            )

            status = result.result
            self.assertTrue(status.is_processing)
            self.assertTrue(status.risk_state == None)

    @responses.activate
    def test_risky_user_state_verified(self):
        self.setup_integration()
        setup_user_action_responses(self.tenant_id, self.user_id)

        actions = [
            (
                IntegrationActionType.DISMISS_RISKY_USER,
                RiskyUserState.DISMISSED,
            ),
            (
                IntegrationActionType.CONFIRM_RISKY_USER,
                RiskyUserState.CONFIRMED,
            ),
        ]

        for action, risk_state in actions:
            # Existing state of the user is dismissed
            responses.get(
                f"https://graph.microsoft.com/v1.0/identityProtection/riskyUsers/{self.user_id}",
                json={"riskState": risk_state.value, "isProcessing": False},
            )

            result = self.integration.invoke_action(
                action,
                UserIdentifierArgs(
                    user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
                ),
            )

            status = result.result
            self.assertFalse(status.is_processing)
            self.assertTrue(status.risk_state == risk_state)

        self.setup_integration()
        setup_user_action_responses(self.tenant_id, self.user_id)

        # Existing state of the user is dismissed
        responses.get(
            f"https://graph.microsoft.com/v1.0/identityProtection/riskyUsers/{self.user_id}",
            json={"riskState": "dismissed", "isProcessing": False},
        )

        result = self.integration.invoke_action(
            IntegrationActionType.DISMISS_RISKY_USER,
            UserIdentifierArgs(
                user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
            ),
        )

        status = result.result
        self.assertFalse(status.is_processing)
        self.assertTrue(status.risk_state == RiskyUserState.DISMISSED)

    @responses.activate
    def test_get_user_info_action(self):
        self.setup_integration()
        setup_user_action_responses(self.tenant_id, self.user_id, self.upn)

        result = self.integration.invoke_action(
            IntegrationActionType.GET_USER_INFO,
            UserIdentifierArgs(
                user_id=UserIdentifier(value_type="azure_ad", value=self.user_id)
            ),
        )

        user = result.result
        self.assertTrue(user.uid == self.user_id)
        self.assertTrue(user.uid_alt == self.upn)
        self.assertTrue(user.display_name == "Test User")
        self.assertTrue(user.is_enabled)

    @responses.activate
    def test_get_user_info_by_upn_action(self):
        self.setup_integration()
        setup_user_action_responses(self.tenant_id, self.user_id, self.upn)

        result = self.integration.invoke_action(
            IntegrationActionType.GET_USER_INFO_BY_UPN,
            UPNIdentifierArgs(
                upn=UPNIdentifier(value_type="azure_ad", value=self.upn),
            ),
        )

        user = result.result
        self.assertTrue(user.uid == self.user_id)
        self.assertTrue(user.uid_alt == self.upn)
        self.assertTrue(user.display_name == "Test User")
        self.assertTrue(user.is_enabled)

    def test_get_external_user_profile_link_action(self):
        self.setup_integration()

        result = self.integration.invoke_action(
            IntegrationActionType.GET_EXTERNAL_USER_PROFILE_LINK,
            UserIdentifierArgs(
                user_id=UserIdentifier(value_type="azure_ad", value="test_entra_id")
            ),
        )

        self.assertTrue(isinstance(result, ExternalUserProfileLinkResult))
        self.assertEqual(
            result.template,
            "https://portal.azure.com/#view/Microsoft_AAD_UsersAndTenants/UserProfileMenuBlade/~/overview/userId/test_entra_id",
        )

    @responses.activate
    def test_get_sign_in_logs_by_user(self):
        self.setup_integration()
        setup_audit_log_action_responses(self.tenant_id)

        # Test getting sign-in logs for a user
        results = self.integration.invoke_action(
            IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID,
            SignInLogsByUserIdArgs(
                user_id=UserIdentifier(value_type="azure_ad", value=self.user_id),
                start_time=datetime(2023, 11, 1, 0, 0, 0, tzinfo=utc),
                end_time=datetime(2023, 12, 1, 23, 59, 59, tzinfo=utc),
            ),
        )

        self.assertTrue(isinstance(results, SignInLogsResult))
        self.assertEqual(len(results.result), 1)
        self.assert_sign_in(results.result[0])

    @responses.activate
    def test_get_sign_in_logs_by_user_upn(self):
        self.setup_integration()
        setup_audit_log_action_responses(self.tenant_id)
        # Test getting sign-in logs for a user
        results = self.integration.invoke_action(
            IntegrationActionType.GET_SIGN_IN_LOGS_BY_UPN,
            SignInLogsByUPNArgs(
                upn=UPNIdentifier(value_type="upn", value=self.upn),
                start_time=datetime(2023, 11, 1, 0, 0, 0, tzinfo=utc),
                end_time=datetime(2023, 12, 1, 23, 59, 59, tzinfo=utc),
            ),
        )
        self.assertTrue(isinstance(results, SignInLogsResult))
        self.assertEqual(len(results.result), 1)
        self.assert_sign_in(results.result[0])

    @responses.activate
    def test_get_sign_in_logs_by_ip(self):
        self.setup_integration()
        setup_audit_log_action_responses(self.tenant_id)

        results = self.integration.invoke_action(
            IntegrationActionType.GET_SIGN_IN_LOGS_BY_IP,
            SignInLogsByIpArgs(
                ip_address=IpAddressIdentifier(value="**************"),
                start_time=datetime(2023, 11, 1, 0, 0, 0, tzinfo=utc),
                end_time=datetime(2023, 12, 1, 23, 59, 59, tzinfo=utc),
            ),
        )
        self.assertTrue(isinstance(results, SignInLogsResult))
        self.assertEqual(len(results.result), 1)
        self.assert_sign_in(results.result[0])

    @patch("apps.connectors.services.artifact_service.ArtifactService.read_lines")
    def test_event_sync_from_artifact(self, m_read_lines):
        lines = get_ms_xdr_events_artifact_lines()
        m_read_lines.return_value = lines
        self.setup_integration()

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC_FROM_ARTIFACT,
            **{
                "artifact_id": "_",
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["vendor_item_ref"]["title"], "test IDP alert")
        self.assertIsNotNone(convert_alert_to_ocsf(result[0]["raw_event"]))

    def assert_sign_in(self, sign_in):
        self.assertTrue(sign_in.class_name == "Authentication")
        self.assertTrue(sign_in.class_uid == 3002)
        self.assertEqual(sign_in.activity_name, "Logon")
        self.assertTrue(sign_in.category_name == "Identity & Access Management")
        self.assertTrue(sign_in.severity_id == 1)
        self.assertTrue(sign_in.severity == "Informational")
        self.assertTrue(sign_in.metadata.product.name == "Microsoft signIn")
        self.assertTrue(sign_in.metadata.product.vendor_name == "Microsoft")
        self.assertEqual(len(sign_in.metadata.profiles), 2)
        self.assertTrue(sign_in.metadata.profiles[0] == "datetime")
        self.assertTrue(sign_in.metadata.profiles[1] == "host")
        self.assertTrue(sign_in.metadata.uid == "66ea54eb-6301-4ee5-be62-ff5a759b0100")
        self.assertTrue(sign_in.actor.app_name == "Test app explorer")
        self.assertTrue(sign_in.actor.app_uid == "9bf03e1f-7082-46e7-962a-61a954e6a4d2")
        self.assertEqual(len(sign_in.actor.authorizations), 2)
        self.assertTrue(
            sign_in.actor.authorizations[0].policy.uid
            == "23e95c9f-7b73-478e-8fb9-f0e7ae90d68b"
        )
        self.assertTrue(
            sign_in.actor.authorizations[0].policy.name
            == "SharePoint limited access for guest workers"
        )
        self.assertTrue(sign_in.actor.authorizations[0].policy.is_applied == False)
        self.assertTrue(
            sign_in.actor.authorizations[1].policy.uid
            == "fd803ba6-c38c-4843-8110-423806a5711b"
        )
        self.assertTrue(
            sign_in.actor.authorizations[1].policy.name == "Medium signin risk block"
        )
        self.assertTrue(sign_in.actor.authorizations[1].policy.is_applied == True)
        self.assertTrue(sign_in.time_dt == datetime(2023, 12, 1, 16, 3, 35, tzinfo=utc))
        self.assertTrue(sign_in.device.name == "DevLab01")
        self.assertTrue(sign_in.device.uid == "d2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2")
        self.assertFalse(sign_in.device.is_compliant)
        self.assertFalse(sign_in.device.is_managed)
        self.assertTrue(sign_in.device.os.name == "Windows 3.1")
        self.assertFalse(sign_in.device.is_trusted)
        self.assertTrue(sign_in.src_endpoint.location.city == "Redmond")
        self.assertTrue(sign_in.src_endpoint.location.region == "Washington")
        self.assertTrue(sign_in.src_endpoint.location.country == "US")
        self.assertTrue(sign_in.src_endpoint.location.lat == 47.68050003051758)
        self.assertTrue(sign_in.src_endpoint.location.long == -122.12094116210938)
        self.assertTrue(sign_in.service.name == "Microsoft Graph")
        self.assertTrue(sign_in.service.uid == "00000003-0000-0000-c000-000000000000")
        self.assertTrue(sign_in.actor.user.full_name == "Test Entra User")
        self.assertTrue(sign_in.actor.user.risk_level_id == 0)
        self.assertTrue(
            sign_in.actor.user.uid == "b45bb39e-d7da-4bc5-b8e7-9202b16882bd"
        )
        self.assertTrue(sign_in.actor.user.uid_alt == "<EMAIL>")
        self.assertTrue(sign_in.status_id == 1)
        self.assertTrue(sign_in.status_code == None)
        self.assertTrue(sign_in.status_detail == None)

        self.assertIsNotNone(sign_in.auth_factors)
        self.assertEqual(len(sign_in.auth_factors), 8)

        factor = sign_in.auth_factors[0]
        self.assertEqual(factor.factor_type_id, 11)
        self.assertEqual(factor.factor_type, "Password")
        self.assertEqual(factor.provider, "Entra ID")

        factor = sign_in.auth_factors[1]
        self.assertEqual(factor.factor_type_id, 99)
        self.assertEqual(factor.factor_type, "previously satisfied")
        self.assertEqual(factor.provider, "Entra ID")

        factor = sign_in.auth_factors[2]
        self.assertEqual(factor.factor_type_id, 1)
        self.assertEqual(factor.factor_type, "SMS")
        self.assertEqual(factor.provider, "Entra ID")

        factor = sign_in.auth_factors[3]
        self.assertEqual(factor.factor_type_id, 3)
        self.assertEqual(factor.factor_type, "Phone Call")
        self.assertEqual(factor.provider, "Entra ID")

        factor = sign_in.auth_factors[4]
        self.assertEqual(factor.factor_type_id, 99)
        self.assertEqual(factor.factor_type, "authenticator app")
        self.assertEqual(factor.provider, "Entra ID")

        factor = sign_in.auth_factors[5]
        self.assertEqual(factor.factor_type_id, 99)
        self.assertEqual(factor.factor_type, "software oath token")
        self.assertEqual(factor.provider, "Entra ID")

        factor = sign_in.auth_factors[6]
        self.assertEqual(factor.factor_type_id, 99)
        self.assertEqual(factor.factor_type, "satisfied by token")
        self.assertEqual(factor.provider, "Entra ID")

        factor = sign_in.auth_factors[7]
        self.assertEqual(factor.factor_type_id, 99)
        self.assertEqual(factor.factor_type, "unknown method")
        self.assertEqual(factor.provider, "Entra ID")


class AzureAdV1HealthCheckTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()

        self._patch_encryption()

        aad_app = AadAppFactory()
        self.connector = ConnectorFactory(
            technology_id="azure_ad", config__client_id=aad_app.client_id
        )
        self.integration = self.connector.get_integration(decrypt_config=False)
        self.tenant_id = self.integration.config["tenant_id"]

    @responses.activate
    def test_test_connection(self):
        setup_host_sync_responses(self.tenant_id)
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_connection_invalid(self):
        responses.post(
            f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token",
            body=OAuthHTTPError("invalid_credentials"),
        )

        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_all_passed(self):
        setup_host_sync_responses(self.integration.config["tenant_id"])
        health_check = ReadAll(self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_read_all_failed(self):
        setup_host_sync_responses(self.integration.config["tenant_id"], roles=[])
        health_check = ReadAll(self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)


class AzureAdV1HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()

        aad_app = AadAppFactory()
        self.connector = ConnectorFactory(
            technology_id="azure_ad",
            config__client_id=aad_app.client_id,
            enabled_actions=[
                IntegrationActionType.HOST_SYNC,
                IntegrationActionType.CONFIRM_RISKY_USER,
                IntegrationActionType.CONFIRM_RISKY_USER_BY_UPN,
                IntegrationActionType.DISMISS_RISKY_USER,
                IntegrationActionType.DISMISS_RISKY_USER_BY_UPN,
                IntegrationActionType.ENABLE_USER_LOGIN,
                IntegrationActionType.ENABLE_USER_LOGIN_BY_UPN,
                IntegrationActionType.DISABLE_USER_LOGIN,
                IntegrationActionType.DISABLE_USER_LOGIN_BY_UPN,
                IntegrationActionType.RESET_USER_PASSWORD,
                IntegrationActionType.RESET_USER_PASSWORD_BY_UPN,
                IntegrationActionType.REVOKE_USER_SESSIONS,
                IntegrationActionType.REVOKE_USER_SESSIONS_BY_UPN,
                IntegrationActionType.GET_USER_INFO,
                IntegrationActionType.GET_USER_INFO_BY_UPN,
                IntegrationActionType.GET_EXTERNAL_USER_PROFILE_LINK,
                IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID,
                IntegrationActionType.GET_SIGN_IN_LOGS_BY_UPN,
                IntegrationActionType.GET_SIGN_IN_LOGS_BY_IP,
            ],
        )
        # self.connector = ConnectorFactory(
        #     technology_id="azure_ad",
        #     config__client_id=aad_app.client_id,
        #     enabled_actions=[
        #         IntegrationActionType.HOST_SYNC,
        #         IntegrationActionType.CONFIRM_RISKY_USER,
        #         IntegrationActionType.DISMISS_RISKY_USER,
        #         IntegrationActionType.ENABLE_USER_LOGIN,
        #         IntegrationActionType.DISABLE_USER_LOGIN,
        #         IntegrationActionType.RESET_USER_PASSWORD,
        #         IntegrationActionType.REVOKE_USER_SESSIONS,
        #         IntegrationActionType.GET_USER_INFO,
        #         IntegrationActionType.GET_EXTERNAL_USER_PROFILE_LINK,
        #         IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID,
        #         IntegrationActionType.GET_SIGN_IN_LOGS_BY_IP,
        #     ],
        # )
        self.integration = self.connector.get_integration(decrypt_config=False)
        self.tenant_id = self.integration.config["tenant_id"]

    @responses.activate
    @patch("django.utils.timezone.now")
    def test_components(self, mnow):
        setup_host_sync_responses(self.integration.config["tenant_id"])
        date_now = datetime(2023, 9, 1, 23, tzinfo=utc)
        mnow.return_value = date_now

        components = HealthCheckComponent.get_components(connector=self.connector)

        critical_checks_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        permissions_checks_expected = [
            HealthCheckRequirement(
                name="Read all directory data",
                description="Read user profiles, groups, group members, etc.",
                value="Directory.Read.All",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        user_read_all = [
            HealthCheckRequirement(
                name="User Read All",
                description="Allows the app to read user profiles",
                value="User Read All",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        user_manage_identities_all = [
            HealthCheckRequirement(
                name="User Manage Identities All",
                description="Allows the app to manage identities of users in the organization",
                value="User Manage Identities All",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        revoke_sessions = [
            HealthCheckRequirement(
                name="Revoke Sessions",
                description="Allows the app to revoke sessions for a user",
                value="Revoke Sessions",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        identity_risky_user_read_write_all = [
            HealthCheckRequirement(
                name="Identity Risky User Read Write All",
                description="Allows the app to read and write risky user information",
                value="Identity Risky User Read Write All",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        audit_logs_read_all = [
            HealthCheckRequirement(
                name="Audit Log Read All",
                description="Allows the app to read audit logs",
                value="Audit Log Read All",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        self.assert_components(
            components,
            [
                critical_checks_expected,
                permissions_checks_expected,
                identity_risky_user_read_write_all,
                identity_risky_user_read_write_all,
                user_manage_identities_all,
                user_manage_identities_all,
                identity_risky_user_read_write_all,
                identity_risky_user_read_write_all,
                user_manage_identities_all,
                user_manage_identities_all,
                user_read_all,
                user_read_all,
                user_manage_identities_all,
                user_manage_identities_all,
                revoke_sessions,
                revoke_sessions,
                audit_logs_read_all,
                audit_logs_read_all,
                audit_logs_read_all,
            ],
        )
