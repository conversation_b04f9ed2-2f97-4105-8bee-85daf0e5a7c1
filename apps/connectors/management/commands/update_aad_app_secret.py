from ata_common.services import encryption_service
from django.core.management import Base<PERSON>ommand

from apps.connectors.models.aad_app import <PERSON><PERSON><PERSON><PERSON>
from core import settings


class Command(BaseCommand):
    help = "Update a client secret for an AadApp"

    def add_arguments(self, parser):
        parser.add_argument(
            "--client_id",
            dest="client_id",
            required=True,
            type=str,
            help="The client_id of the application to update",
        )
        parser.add_argument(
            "--client_secret",
            dest="client_secret",
            required=False,
            type=str,
            help="The new value of the secret",
        )
        parser.add_argument(
            "--preview",
            dest="preview",
            required=False,
            action="store_true",
            help="If preview is specified, changes will only be printed, not saved to the database",
        )

    def handle(self, *args, **options):
        try:
            self._try_handle(*args, **options)
        except Exception as e:
            print(f"Caught exception {e}")

    def _try_handle(self, *args, **options):
        client_id = options["client_id"]
        client_secret = options["client_secret"]
        preview = options["preview"]

        if not preview and not client_secret:
            print("If not in preview mode, the new client_secret must be specified")
            return

        apps = AadApp.objects.filter(client_id=client_id)

        for app in apps:
            print(f"Found AadApp {app} with client_id={client_id}... ", end="")
            encrypted_secret = encryption_service.encrypt(
                value=client_secret, key=settings.AWS_KMS_CONFIG_SECRETS_KEY
            )
            if app.client_secret == encrypted_secret:
                print("secret is already the requested value, skipping")
            elif preview:
                print("would be updated (running in preview mode)")
            else:
                print("updating")
                app.client_secret = encrypted_secret
                app.save(update_fields=["client_secret"])

        print("Done")
