import copy
import logging
from uuid import UUID, uuid4

from criticalstart.service_bus import notifications
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>ield
from django.db import models
from django.utils import timezone

from apps.accounts.models import Account, Organization
from apps.connectors.activity_logger import Null<PERSON>andler
from apps.connectors.integrations import (
    IntegrationActionType,
    IntegrationLogger,
    Template,
    TemplateVersion,
)
from apps.connectors.integrations.vendors.vendor import Vendors
from apps.connectors.models.connection import Connection

from .activity_log import DatabaseLogHandler

logger = logging.getLogger(__name__)


class ConnectorManager(models.Manager):
    def with_enabled_action(
        self, organization_id: UUID, action_type: IntegrationActionType
    ):
        """
        Returns all connectors for the given organization that have the specified action enabled.
        """
        return self.filter(
            organization_id=organization_id,
            enabled=True,
            enabled_actions__contains=[action_type.value],
        )


class Connector(models.Model):
    objects = ConnectorManager()

    class CoverageMode(models.TextChoices):
        ENABLED = "enabled"
        IGNORE = "ignore"
        NOT_APPLICABLE = "n/a"

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)

    technology_id = models.CharField(max_length=255)
    version_id = models.CharField(max_length=255)

    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT)

    name = models.CharField(max_length=255, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_deleted = models.BooleanField(default=False)

    connection = models.ForeignKey(
        Connection, on_delete=models.RESTRICT, null=True, blank=True
    )

    settings = models.JSONField(default=dict, blank=True)

    bookmarks = models.JSONField(default=dict, blank=True)

    enabled = models.BooleanField(default=True)

    vulnerability_coverage_mode = models.CharField(
        choices=CoverageMode.choices,
        default=CoverageMode.NOT_APPLICABLE,
        max_length=255,
    )

    endpoint_coverage_mode = models.CharField(
        choices=CoverageMode.choices,
        default=CoverageMode.NOT_APPLICABLE,
        max_length=255,
    )

    enabled_actions = ArrayField(
        models.CharField(max_length=255),
        default=list,
        blank=True,
    )

    # Internal connectors are not available via public API. A connector can be marked
    # as internal via Django or when creating it via internal API. This field is ignored
    # when template is marked as internal. See is_internal property.
    internal = models.BooleanField(default=False)

    last_activity_at = models.DateTimeField(
        null=True, help_text="Last time this connector was successfully used"
    )

    product_id = models.IntegerField(null=True, blank=True, default=None)

    config_managed_by_portal = models.BooleanField(default=False)

    class Meta:
        indexes = [
            models.Index(fields=["organization_id", "technology_id", "version_id"]),
            models.Index(fields=["organization_id"]),
            models.Index(fields=["technology_id", "version_id"]),
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # rudimentary change tracking
        self._original_is_deleted = self.is_deleted
        self._original_enabled = self.enabled
        self._original_settings = copy.deepcopy(self.settings)
        self._original_enabled_actions = self.enabled_actions.copy()
        self._original_vulnerability_coverage_mode = self.vulnerability_coverage_mode
        self._original_endpoint_coverage_mode = self.endpoint_coverage_mode

    # FIXME: migration_id=connection_config
    @property
    def config(self):
        return self.connection.config

    # FIXME: migration_id=connection_config
    @config.setter
    def config(self, value):
        if value is not None:
            self.migrate_config_to_connection(value)
            self.connection.config = value

    @property
    def template(self) -> Template:
        return Template.get_template(self.technology_id)

    @property
    def template_version(self) -> TemplateVersion:
        return self.template.versions[self.version_id]

    def get_integration(self, decrypt_config=True):
        template_version = self.template_version

        config = (
            self.connection.decrypted_config
            if decrypt_config
            else self.connection.config
        )

        integration = template_version.integration(
            config,
            template_version.settings_model(self.settings),
            IntegrationLogger(
                DatabaseLogHandler(self.id) if self.id else NullHandler()
            ),
            template_version.bookmarks_model(self.bookmarks)
            if template_version.bookmarks_model
            else None,
            str(self.id),
        )
        return integration

    @property
    def is_internal(self) -> bool:
        return self.template.is_internal or self.internal

    @property
    def category(self) -> Template.Category.Category:
        return self.template.category

    @property
    def category_id(self) -> str:
        return self.category.id

    @property
    def category_name(self) -> str:
        return self.category.name

    @property
    def vendor(self) -> Vendors.Vendor:
        return self.template.vendor

    @property
    def vendor_id(self) -> str:
        return self.vendor.id

    @property
    def vendor_name(self) -> str:
        return self.vendor.name

    @property
    def modules(self) -> list[str]:
        return self.template_version.modules

    @property
    def supported_actions(self) -> list:
        return self.template_version.supported_actions

    @property
    def account(self) -> Account:
        return self.organization.account

    @property
    def account_id(self) -> uuid4:
        return self.organization.account_id

    @property
    def technology_name(self) -> str:
        return self.template.name

    def update_last_activity_at(self):
        self.last_activity_at = timezone.now()
        self.save(update_fields=["last_activity_at"])

    def should_limit_one_per_organization(self):
        """
        Check if the template is limited to one connector per organization.
        """

        event_fetching_actions = {
            IntegrationActionType.EVENT_SYNC,
            IntegrationActionType.EVENT_SYNC_FROM_ARTIFACT,
        }

        return set(self.supported_actions).intersection(event_fetching_actions)

    @property
    def log_extra(self):
        return {
            "connector_id": self.id,
            "org_id": self.organization_id,
            "org_alias": self.organization.alias,
            "technology_id": self.technology_id,
        }

    def ensure_coverage_modes_from_template(self):
        """
        Ensure the vulnerability and endpoint coverage modes are set correctly based on the template before saving.
        """
        self.vulnerability_coverage_mode = self.get_vulnerability_coverage_mode(
            self.vulnerability_coverage_mode, self.template
        )
        self.endpoint_coverage_mode = self.get_endpoint_coverage_mode(
            self.endpoint_coverage_mode, self.template
        )

    @property
    def connection_template_id(self):
        return self.template_version.connection_model.id

    def migrate_config_to_connection(self, config):
        if self.connection:
            return

        connection = Connection.objects.create(
            organization=self.organization,
            connection_template_id=self.connection_template_id,
            config=config,
        )
        self.connection = connection

    def save(self, *args, **kwargs):
        # FIXME: migration_id=connection_config
        #####################################################################
        if self.config:
            self.connection.config = self.config
            self.connection.save(update_fields=["config"])

            # We need to remove "config" from "update_fields", because
            # config is no longer a field on Connector.
            if "update_fields" in kwargs:
                update_fields = set(kwargs["update_fields"])
                update_fields.discard("config")
                if not update_fields:
                    del kwargs["update_fields"]  # pragma: no cover
                else:
                    kwargs["update_fields"] = update_fields
        #####################################################################

        # fixup any data that needs to be fixed before saving
        self.ensure_coverage_modes_from_template()

        is_adding = self._state.adding

        send_created_notification = is_adding
        send_update_notification = False
        send_delete_notification = False

        if not is_adding:
            # Notify upon significant updates
            if self._original_enabled != self.enabled:
                send_update_notification = True
            if self._original_enabled_actions != self.enabled_actions:
                send_update_notification = True
            if self._original_settings != self.settings:
                send_update_notification = True
            if (
                self._original_vulnerability_coverage_mode
                != self.vulnerability_coverage_mode
            ):
                send_update_notification = True
            if self._original_endpoint_coverage_mode != self.endpoint_coverage_mode:
                send_update_notification = True

            # Check if is_deleted changed from False to True
            if self._original_is_deleted is False:
                if self.is_deleted is True:
                    send_delete_notification = True

        # This is meant to be a temporary measure until Portal can support
        # multiple of the same product per organization.
        if is_adding and self.should_limit_one_per_organization():
            if (
                Connector.objects.filter(
                    organization=self.organization,
                    technology_id=self.technology_id,
                    is_deleted=False,
                )
                .exclude(id=self.id)
                .exists()
            ):
                raise ValueError(
                    f"Only one {self.technology_name} integration is allowed per organization"
                )

        super().save(*args, **kwargs)

        if send_created_notification:
            notifications.IntegrationCreated(
                object=notifications.Integration(
                    id=self.id,
                    account_id=self.account_id,
                    organization_id=self.organization.id,
                )
            ).publish(key=self.organization.id)

        if send_update_notification:
            notifications.IntegrationUpdated(
                object=notifications.Integration(
                    id=self.id,
                    account_id=self.account_id,
                    organization_id=self.organization.id,
                )
            ).publish(key=self.organization.id)

        if send_delete_notification:
            notifications.IntegrationDeleted(
                object=notifications.Integration(
                    id=self.id,
                    account_id=self.account_id,
                    organization_id=self.organization.id,
                )
            ).publish(key=self.organization.id)

    @staticmethod
    def get_vulnerability_coverage_mode(
        vulnerability_coverage_mode: CoverageMode,
        template: Template,
    ) -> CoverageMode:
        """
        Gets the forced vulnerability coverage mode based on the template.

        If the template does not support vulnerability coverage, sets the mode to NOT_APPLICABLE.
        If the mode is NOT_APPLICABLE and the template supports vulnerability coverage, sets the mode to IGNORE.
        """
        if not template.vulnerability_coverage_available:
            return Connector.CoverageMode.NOT_APPLICABLE
        elif vulnerability_coverage_mode == Connector.CoverageMode.NOT_APPLICABLE:
            return Connector.CoverageMode.ENABLED
        return vulnerability_coverage_mode

    @staticmethod
    def get_endpoint_coverage_mode(
        endpoint_coverage_mode: CoverageMode,
        template: Template,
    ) -> CoverageMode:
        """
        Gets the forced endpoint coverage mode based on the template.

        If the template does not support endpoint coverage, sets the mode to NOT_APPLICABLE.
        If the mode is NOT_APPLICABLE and the template supports endpoint coverage, sets the mode to IGNORE.
        """
        if not template.endpoint_coverage_available:
            return Connector.CoverageMode.NOT_APPLICABLE
        elif endpoint_coverage_mode == Connector.CoverageMode.NOT_APPLICABLE:
            return Connector.CoverageMode.ENABLED
        return endpoint_coverage_mode
