from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    HostIdentifierArgs,
    Message,
    TAPResult,
    ocsf,
)


class AddHostToWatchlistResult(TAPResult[Message]):
    """Result of the AddHostToWatchlist action."""

    ...


class AddHostToWatchlist(IntegrationAction):
    name = "Add Host to Watchlist"
    action_type = IntegrationActionType.ADD_HOST_TO_WATCHLIST
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=HostIdentifierArgs,
        result_type=AddHostToWatchlistResult,
    )


class RemoveHostFromWatchlistResult(TAPResult[Message]):
    """Result of the RemoveHostFromWatchlist action."""

    ...


class RemoveHostFromWatchlist(IntegrationAction):
    name = "Remove Host from Watchlist"
    action_type = IntegrationActionType.REMOVE_HOST_FROM_WATCHLIST
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=HostIdentifierArgs,
        result_type=RemoveHostFromWatchlistResult,
    )


class HostInfoResult(TAPResult[ocsf.NetworkEndpoint]):
    """Result object for host information actions."""

    ...


class GetHostInfoAction(IntegrationAction):
    """
    Base action class for retrieving host information.

    This class defines the common properties for all host info actions.
    Integration-specific implementations should inherit from this class
    and implement the execute() method.
    """

    name = "Get host info"
    action_type = IntegrationActionType.GET_HOST_INFO
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=HostIdentifierArgs,
        result_type=HostInfoResult,
    )
