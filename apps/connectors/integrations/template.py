from dataclasses import dataclass
from functools import cached_property
from typing import Protocol

from criticalstart.auth.v2.models import Organization
from pydantic import BaseModel

from apps.connectors.integrations.actions.action import FetchIntegrationActionType
from apps.connectors.integrations.vendors.vendor import Vendors
from apps.connectors.utils import HashableById

from .bookmarks import TemplateVersionBookmarks
from .integration import Integration
from .settings import TemplateVersionSettings
from .types import EncryptedStr


class _Required:
    pass


class BaseConnectionTemplate(HashableById):
    id = _Required()
    name = _Required()
    config_model = _Required()


class ConnectionTemplate(BaseConnectionTemplate):
    _templates = {}

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        cls.validate(cls)
        cls._templates[cls.id] = cls()

    @classmethod
    def get_template(cls, connection_template_id):
        return cls._templates[connection_template_id]

    @staticmethod
    def validate(new_cls):
        attrs = [
            attr
            for attr in dir(BaseConnectionTemplate)
            if isinstance(getattr(ConnectionTemplate, attr), _Required)
        ]
        if any(isinstance(getattr(new_cls, attr), _Required) for attr in attrs):
            raise NotImplementedError(
                f"Connection Template class must have all attributes defined: {attrs}"
            )

    @cached_property
    def vendor_id(self):
        vendor_template = next(
            Template.get_templates(connection_template_ids=[self.id])
        )
        return vendor_template.vendor.id


class TemplateDatabaseValidationMixin:
    def validate_database(self, org: Organization = None):
        pass


class EncryptedTemplateMixin(BaseModel):
    """
    When converting a TemplateVersionConfig to a dict, EncryptedStr values are automatically
    converted to a masked "********" string. This mixin allows to get an unmasked value, either
    encrypted or decrypted.
    """

    def _get_config(self, decrypt=False):
        config = self.model_dump(mode="json")
        for name, info in self.model_fields.items():
            if info.annotation == EncryptedStr:
                encrypted_str: EncryptedStr = getattr(self, name)
                if encrypted_str.is_mask():
                    # If the value is the mask, this means the user wants to retain the
                    # current value. Remove it from the config.
                    config.pop(name)
                else:
                    config[name] = encrypted_str.get_secret_value(decrypt=decrypt)
        return config

    @property
    def decrypted_config(self):
        return self._get_config(decrypt=True)

    @property
    def unmasked_config(self):
        return self._get_config(decrypt=False)


class TemplateVersionConfig(
    EncryptedTemplateMixin, TemplateDatabaseValidationMixin, BaseModel
):
    pass


class EmptyConfig(TemplateVersionConfig):
    pass


class TemplateVersionActionSettings(BaseModel):
    pass


class TemplateVersionActionBookmark(BaseModel):
    pass


class TemplateVersion:
    integration: type[Integration] = _Required()
    id = _Required()
    name = _Required()
    # We are transitioning the config from being directly on the TemplateVersion to being defined on a ConnectionTemplate.
    # Eventually, config_model should be removed and connection_model should be required.
    # Until then, connection_model is optional and config_model must be EmptyConfig for any templates that
    # define a connection_model.
    connection_model: type[ConnectionTemplate]
    config_model = _Required()

    settings_model: type[TemplateVersionSettings] = _Required()
    bookmarks_model: type[TemplateVersionBookmarks] = None

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        cls.validate(cls)

    @staticmethod
    def validate(new_cls):
        attrs = [
            attr
            for attr in dir(TemplateVersion)
            if isinstance(getattr(TemplateVersion, attr), _Required)
        ]
        if any(isinstance(getattr(new_cls, attr), _Required) for attr in attrs):
            raise NotImplementedError(
                f"TemplateVersion class must have all attributes defined: {attrs}"
            )

        connection_model = getattr(new_cls, "connection_model", None)
        if not connection_model:
            raise NotImplementedError(
                f"connection_model not set for TemplateVersion {new_cls.__name__}"
            )  # pragma: nocover

        config_model_type = getattr(new_cls, "config_model")
        # During the transition, config_model_type must match the model of the connection_model.
        connection_config_model = getattr(connection_model, "config_model", None)
        if (
            config_model_type != connection_config_model
            and config_model_type != EmptyConfig
        ):
            raise NotImplementedError(
                f"config_model on TemplateVersion {new_cls.__name__} must match the model of the connection_model"
            )  # pragma: nocover

    @property
    def supported_actions(self):
        return [action.action_type for action in self.integration.actions]

    @property
    def modules(self) -> list[str]:
        modules = {action.entitlement for action in self.integration.actions}
        return list(modules)

    def has_module(self, modules: list[str]) -> bool:
        return any([m in self.modules for m in modules])

    @classmethod
    def default_settings(cls):
        return cls.settings_model.model_validate({}).model_dump(mode="json")

    @property
    def connection_template_id(self):
        return self.connection_model.id if self.connection_model else None

    @property
    def connection_config_model(self):
        return (
            self.connection_model.config_model.model_json_schema()
            if self.connection_model
            else None
        )

    @property
    def actions_metadata(self):
        schema = self.settings_model.model_json_schema()
        return {
            action.action_type: {
                "name": action.name,
                "entitlement": action.entitlement,
                "settings": schema["properties"][action.action_type],
            }
            for action in self.integration.actions
        }


class BaseTemplate:
    class Category:
        @dataclass(frozen=True)
        class Category:
            id: str
            name: str

        ASSET_SOURCE = Category(
            "asset_source",
            "Asset Sources",
        )
        CLOUD_SECURITY = Category(
            "cloud_security",
            "Cloud Security",
        )
        DATA_BACKUP = Category(
            "data_backup",
            "Data Backup",
        )
        EMAIL_SECURITY = Category(
            "email_security",
            "Email Security",
        )
        ENDPOINT_SECURITY = Category(
            "endpoint_security",
            "Endpoint Security",
        )
        IDENTITY_SECURITY = Category(
            "identity_security",
            "Identity Security",
        )
        SUPPORTING = Category(
            "supporting",
            "Supporting",
        )
        NETWORK_SECURITY = Category(
            "network_security",
            "Network Security",
        )
        SAAS_SECURITY = Category(
            "saas_security",
            "SaaS Security",
        )
        SIEM = Category(
            "siem",
            "SIEM",
        )
        OT_SECURITY = Category(
            "ot_security",
            "OT Security",
        )
        PLATFORM = Category(
            "platform",
            "Platform",
        )
        COMPLIANCE = Category(
            "compliance",
            "Compliance",
        )
        IT_OPERATIONS = Category(
            "it_operations",
            "IT Ops",
        )
        INTELLIGENCE_AND_ANALYSIS = Category(
            "intelligence_and_analysis",
            "Intelligence & Analysis",
        )
        VULNERABILITY_MANAGEMENT = Category(
            "vulnerability_management",
            "Vulnerability Management",
        )

    id = _Required()
    name = _Required()
    category: Category.Category = _Required()
    vendor: Vendors.Vendor = _Required()
    versions = _Required()
    vulnerability_coverage_available = False
    endpoint_coverage_available = False

    @property
    def is_alert_source(self):
        alert_source_actions = [
            FetchIntegrationActionType.EVENT_SYNC,
            FetchIntegrationActionType.EVENT_SYNC_FROM_ARTIFACT,
        ]
        for version in self.versions.values():
            supported_actions = version.supported_actions
            if any(action in supported_actions for action in alert_source_actions):
                return True

        return False

    @property
    def is_internal(self):
        return False

    @property
    def category_id(self):
        return self.category.id

    @property
    def category_name(self):
        return self.category.name

    @property
    def vendor_id(self):
        return self.vendor.id

    @property
    def vendor_name(self):
        return self.vendor.name

    @staticmethod
    def validate(new_cls):
        attrs = [
            attr
            for attr in dir(BaseTemplate)
            if isinstance(getattr(Template, attr), _Required)
        ]
        if any(isinstance(getattr(new_cls, attr), _Required) for attr in attrs):
            raise NotImplementedError(
                f"Template {new_cls.__name__} must have all attributes defined: {attrs}"
            )


class Template(BaseTemplate):
    _templates = {}

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        if cls.__name__ == "Template":
            return
        cls.validate(cls)
        cls._templates[cls.id] = cls()

    @classmethod
    def get_template(cls, technology_id):
        return cls._templates[technology_id]

    @classmethod
    def get_all_templates(cls):
        return cls._templates.values()

    @classmethod
    def get_templates(
        cls, vendor_ids=None, category_ids=None, connection_template_ids=None
    ):
        for template in cls.get_all_templates():
            if vendor_ids and template.vendor.id not in vendor_ids:
                continue
            if category_ids and template.category.id not in category_ids:
                continue
            if connection_template_ids:
                if not any(
                    version.connection_template_id in connection_template_ids
                    for version in template.versions.values()
                ):
                    continue
            yield template


class TemplateVersionId(Protocol):
    technology_id: str
    version_id: str
