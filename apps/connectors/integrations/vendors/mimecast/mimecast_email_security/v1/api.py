import time

from apps.connectors.integrations.api import ApiBase
from apps.connectors.integrations.vendors.mimecast.mimecast_email_security.v1.connection import (
    MimecastURL,
)


def get_params(page_size=100, page_token=None, from_date=None, to_date=None):
    pagination = {}
    if page_size:
        pagination["pageSize"] = page_size
    if page_token:
        pagination["pageToken"] = page_token
    data = {}
    if from_date:
        data["from"] = from_date
    if to_date:
        data["to"] = to_date
    params = {"meta": {"pagination": pagination}, "data": [data]}
    return params


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    response = response.get("result", response)
    results = response["data"]
    yield results
    items_count = len(results)
    page_token = response.get("meta", {}).get("pagination", {}).get("next", None)

    while items_count > 0 and page_token is not None:
        response = bound_method(page_token=page_token, **kwargs)
        response = response.get("result", response)
        results = response["data"]
        yield results
        items_count = len(results)
        page_token = response.get("meta", {}).get("pagination", {}).get("next", None)


class MimecastEmailSecurityV1Api(ApiBase):
    def __init__(
        self, base_url=MimecastURL.GLOBAL_URL, client_id=None, client_secret=None
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.token_expiry = None
        super().__init__(
            base_url=base_url,
            static_headers={
                "Content-Type": "application/json",
            },
        )

    @property
    def authorized_client(self):
        if not self.access_token or time.time() >= self.token_expiry:
            self.token_expiry = time.time() + 1800  # Token expiry set to 30 min
            url = self.url("oauth/token")
            self.session.headers.update(
                {"Content-Type": "application/x-www-form-urlencoded"}
            )
            params = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret,
            }
            response = self.session.post(url, params=params)
            self.session.headers.update(
                {
                    "Authorization": f"Bearer {response.text}",
                    "Content-Type": "application/json",
                }
            )
        return self.session

    def message_finder_search(self, **kwargs):
        """
        Call the Message Finder (Tracking) API: /api/message-finder/search
        https://integrations.mimecast.com/documentation/endpoint-reference/message-finder-formerly-tracking/search/
        """
        path = "/api/message-finder/search"
        url = self.url(path)
        response = self.authorized_client.post(url)
        return response.json()

    def get_siem_logs(self, **kwargs):
        """
        Call the SIEM Logs API: /api/audit/get-siem-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-siem-logs/
        """
        path = "/api/audit/get-siem-logs"
        url = self.url(path)
        response = self.authorized_client.post(url, json=kwargs)
        return response.json()

    def get_ttp_url_logs(
        self, page_size=100, page_token=None, from_date=None, to_date=None, **kwargs
    ):
        """
        Call the TTP URL Logs API: /api/ttp/url/get-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-ttp-url-logs/
        """
        path = "/api/ttp/url/get-logs"
        url = self.url(path)
        response = self.authorized_client.post(
            url,
            json=get_params(page_size, page_token, from_date, to_date),
        )
        return response.json()

    def get_ttp_attachment_logs(
        self, page_size=100, page_token=None, from_date=None, to_date=None, **kwargs
    ):
        """
        Call the TTP Attachment Protection Logs API: /api/ttp/attachment/get-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-ttp-attachment-protection-logs/
        """
        path = "/api/ttp/attachment/get-logs"
        url = self.url(path)
        response = self.authorized_client.post(
            url,
            json=get_params(page_size, page_token, from_date, to_date),
        )
        return response.json()

    def get_ttp_impersonation_logs(
        self, page_size=100, page_token=None, from_date=None, to_date=None, **kwargs
    ):
        """
        Call the TTP Impersonation Protection Logs API: /api/ttp/impersonation/get-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-ttp-impersonation-protect-logs/
        """
        path = "/api/ttp/impersonation/get-logs"
        url = self.url(path)
        response = self.authorized_client.post(
            url,
            json=get_params(page_size, page_token, from_date, to_date),
        )
        return response.json()

    def decode_url(self, url_to_decode):
        """
        Call the TTP URL Decode API: /api/ttp/url/decode-url
        https://integrations.mimecast.com/documentation/endpoint-reference/targeted-threat-protection-url-protect/decode-url/
        """
        path = "/api/ttp/url/decode-url"
        api_url = self.url(path)
        data = {"data": [{"url": url_to_decode}]}
        response = self.authorized_client.post(api_url, json=data)
        return response.json()
