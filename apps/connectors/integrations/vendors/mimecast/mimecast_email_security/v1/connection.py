from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class MimecastURL(StrEnum):
    GLOBAL_URL = "https://api.services.mimecast.com"
    UK_INSTANCE_URL = "https://uk-api.services.mimecast.com"
    US_INSTANCE_URL = "https://us-api.us.mimecast.com"


class MimecastEmailSecurityV1Config(TemplateVersionConfig):
    base_url: MimecastURL = Field(
        title="Mimecast API URL",
        description="The base URL of the Mimecast API.",
    )
    client_id: str = Field(
        title="Client ID",
        description="Client ID value received when you registered your application.",
    )
    client_secret: EncryptedStr = Field(
        title="Client Secret",
        description="Client Secret value received when you registered your application.",
    )


class MimecastEmailSecurityV1Connection(ConnectionTemplate):
    id = "mimecast_email_security"
    name = "Mimecast Advanced Email Security"
    config_model = MimecastEmailSecurityV1Config
