import logging
from datetime import datetime
from typing import Generator, List

import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRefExtended,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.constants import (
    CrowdstrikeProducts,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.utils import (
    generate_fql_filter,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.bookmarks import (
    CrowdstrikeFalconIpV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadAlerts,
)

logger = logging.getLogger(__name__)


def map_ocsf_severity(severity) -> ocsf.Severity:
    """
    Map CrowdStrile Alert severity to OCSF severity
    """
    if not isinstance(severity, (int, float)):
        return ocsf.Severity.UNKNOWN

    if severity < 20:
        return ocsf.Severity.INFORMATIONAL
    elif severity < 40:
        return ocsf.Severity.LOW
    elif severity < 60:
        return ocsf.Severity.MEDIUM
    elif severity < 80:
        return ocsf.Severity.HIGH
    elif severity >= 80:
        return ocsf.Severity.CRITICAL


def map_ocsf_status(status: str) -> ocsf.DetectionStatus:
    """
    Map CrowdStrike Alert status to OCSF DetectionStatus
    """
    if not status:
        return ocsf.DetectionStatus.UNKNOWN

    if status == "new":
        return ocsf.DetectionStatus.NEW
    elif status == "in_progress":
        return ocsf.DetectionStatus.IN_PROGRESS
    elif status == "closed":
        return ocsf.DetectionStatus.RESOLVED
    elif status == "reopened":  # OCSF does not have a direct mapping for this. Use NEW
        return ocsf.DetectionStatus.NEW

    return ocsf.DetectionStatus.UNKNOWN


def map_ocsf_evidences(event: dict) -> List[ocsf.EvidenceArtifacts]:
    # Create actor evidence
    account = None
    if event.get("source_account_azure_id"):
        account = ocsf.Account(
            type=ocsf.AccountType.AZURE_AD_ACCOUNT,
        )
    elif event.get("source_account_okta_id"):
        account = ocsf.Account(
            type="Okta",
        )

    source_user_id = event.get("source_account_object_guid")
    source_user_id_alt = event.get("source_account_upn")
    if not source_user_id_alt:
        source_user_id_alt = (
            event.get("source_account_azure_id")
            or event.get("source_account_okta_id")
            or event.get("source_account_object_sid")
        )

    actor = ocsf.Actor(
        user=ocsf.User(
            uid=source_user_id or "",
            uid_alt=source_user_id_alt,
            domain=event.get("source_account_domain"),
            name=event.get("source_account_name", ""),
            account=account,
        ),
    )

    # Create device evidence
    device = None
    if (
        event.get("location_latitude")
        or event.get("location_longitude")
        or event.get("location_country_code")
    ):
        device = ocsf.Device(
            location=ocsf.GeoLocation(
                lat=event.get("location_latitude"),
                long=event.get("location_longitude"),
                country=event.get("location_country_code"),
            ),
        )

    # create source endpoint evidence
    src_endpoint = ocsf.NetworkEndpoint(
        owner=ocsf.User(
            uid=event.get("source_endpoint_account_object_guid", ""),
            uid_alt=event.get("source_endpoint_account_object_sid"),
        ),
        hostname=event.get("source_endpoint_host_name"),
        ip=event.get("source_endpoint_ip_address"),
        uid=event.get("source_endpoint_sensor_id", ""),
    )

    # create user evidence
    user = ocsf.User(
        uid=event.get("target_account_object_sid") or "",
        uid_alt=event.get("target_account_upn"),
        domain=event.get("target_account_domain"),
        name=event.get("target_account_name", ""),
    )

    # create destination endpoint evidence
    dst_endpoint = ocsf.NetworkEndpoint(
        owner=ocsf.User(
            uid=event.get("target_endpoint_account_object_guid") or "",
            uid_alt=event.get("target_endpoint_account_object_sid"),
        ),
        hostname=event.get("target_endpoint_host_name"),
        ip=event.get("target_endpoint_ip_address"),
        uid=event.get("target_endpoint_sensor_id", ""),
    )

    evidence_artifact = ocsf.EvidenceArtifacts(
        actor=actor,
        src_endpoint=src_endpoint,
        device=device,
        dst_endpoint=dst_endpoint,
        user=user,
    )

    return [evidence_artifact]


def convert_to_ocsf(
    event: dict, updated_timestamp: datetime = None
) -> ocsf.DetectionFinding:
    activity = (
        ocsf.DetectionActivity.UPDATE
        if updated_timestamp
        else ocsf.DetectionActivity.CREATE
    )
    attempt_outcome = event.get("attempt_outcome")
    verdict = (
        ocsf.Verdict.TRUE_POSITIVE
        if attempt_outcome == "True"
        else ocsf.Verdict.UNKNOWN
    )

    # Map cloud information
    cloud = None
    azure_application_id = event.get("azure_application_id")
    if azure_application_id:
        cloud = ocsf.Cloud(
            account=ocsf.Account(
                uid=azure_application_id,
                type=ocsf.AccountType.AZURE_AD_ACCOUNT,
            ),
            provider="Azure",
        )
    okta_application_id = event.get("okta_application_id")
    if okta_application_id:
        cloud = ocsf.Cloud(
            account=ocsf.Account(
                uid=okta_application_id,
                type="Okta",
            ),
            provider="Okta",
        )

    # Map tactic and technique
    tactic = (
        ocsf.Tactic(
            name=event.get("tactic"),
            uid=event.get("tactic_id"),
        )
        if event.get("tactic") or event.get("tactic_id")
        else None
    )

    technique = (
        ocsf.Technique(
            name=event.get("technique"),
            uid=event.get("technique_id"),
        )
        if event.get("technique") or event.get("technique_id")
        else None
    )

    # Map policy
    policy = None
    if event.get("idp_policy_rule_id") or event.get("idp_policy_rule_name"):
        policy = ocsf.Policy(
            uid=event.get("idp_policy_rule_id"),
            name=event.get("idp_policy_rule_name"),
        )

    finding = ocsf.DetectionFinding(
        activity=activity,
        cloud=cloud,
        confidence_score=event.get("confidence"),
        end_time_dt=event.get("end_time"),
        evidences=map_ocsf_evidences(event),
        finding_info=ocsf.FindingInformation(
            created_time_dt=event.get("created_timestamp"),
            desc=event.get("description"),
            title=event.get("display_name"),
            src_url=event.get("falcon_host_link"),
            uid=event.get("composite_id"),
            analytic=ocsf.Analytic(
                uid=str(event.get("pattern_id")) if event.get("pattern_id") else "",
            ),
            product=ocsf.Product(
                name=event.get("source_products", [""])[0],
                feature=ocsf.Feature(
                    name=event.get("product", ""),
                ),
                vendor_name=event.get("source_vendors", [""])[0],
            ),
            attacks=(
                [
                    ocsf.MitreAttack(
                        tactic=tactic,
                        technique=technique,
                    )
                ]
                if tactic or technique
                else []
            ),
            modified_time_dt=event.get("updated_timestamp"),
        ),
        message=event.get("description"),
        metadata=ocsf.Metadata(
            tenant_uid=event.get("cid"),
            correlation_uid=event.get("composite_id"),
            event_code=event.get("type"),
            profiles=["cloud", "datetime", "incident"],
            product=ocsf.Product(
                name="CrowdStrike Falcon Identity Protection",
                feature=ocsf.Feature(
                    name=event.get("product"),
                ),
                vendor_name="CrowdStrike",
            ),
        ),
        policy=policy,
        severity=map_ocsf_severity(event.get("severity")),
        start_time_dt=event.get("start_time"),
        status=map_ocsf_status(event.get("status")),
        time_dt=event.get("timestamp"),
        verdict=verdict,
    )

    return finding


def normalize_event(event: dict) -> Event:
    mitre_techniques = [event.get("technique_id")]

    # Convert to OCSF
    ocsf = convert_to_ocsf(event)

    # Create Event object
    result = Event(
        event_timestamp=event.get("timestamp"),
        raw_event=event,
        ocsf=ocsf,
        vendor_item_ref=VendorRefExtended(
            id=event.get("composite_id"),
            title=event.get("display_name") or event.get("name"),
            url=event.get("falcon_host_link"),
            created=datetime.fromisoformat(event.get("created_timestamp")),
        ),
        vendor_group_ref=None,
        ioc=EventIOCInfo(
            external_id=str(event.get("pattern_id")),
            external_name=event.get("display_name") or event.get("name"),
            has_ioc_definition=False,
            mitre_techniques=mitre_techniques,
        ),
    )
    return result


class CrowdstrikeFalconIpEventSync(EventSync):
    PAGE_SIZE = 1000

    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: CrowdstrikeFalconIpV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()

        body = self._generate_request_body(bookmark)
        idx = 0
        for idx, alert in enumerate(paginate(api.fetch_alerts, body=body)):
            yield alert
            timestamp = alert["created_timestamp"]
            bookmark.last_event_ingested = datetime.fromisoformat(timestamp)
        logger.debug(f"{idx} alerts fetched")

    def get_permission_checks(self):
        return [ReadAlerts]  # pragma: no cover

    def _generate_request_body(self, bookmark: CrowdstrikeFalconIpV1EventSyncBookmark):
        return {
            "include_hidden": False,
            "filter": generate_fql_filter(
                products=[CrowdstrikeProducts.IDP],
                created_after=bookmark.last_event_ingested,
            ),
            "sort": "created_timestamp.asc",
            "limit": self.PAGE_SIZE,
        }
