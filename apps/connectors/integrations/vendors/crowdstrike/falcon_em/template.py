from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import FalconEmV1TemplateVersion


class FalconEmTemplate(Template):
    id = "falcon_em"
    name = "CrowdStrike Falcon Exposure Management"
    category = Template.Category.ASSET_SOURCE
    versions = {
        FalconEmV1TemplateVersion.id: FalconEmV1TemplateVersion(),
    }
    vulnerability_coverage_available = True
    vendor = Vendors.CROWDSTRIKE
