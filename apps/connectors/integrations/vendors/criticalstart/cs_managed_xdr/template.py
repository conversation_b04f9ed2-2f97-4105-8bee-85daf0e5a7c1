from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class CsManagedXdrTemplate(Template):
    id = "cs_managed_xdr"
    name = "Critical Start Managed XDR"
    category = Template.Category.SIEM
    versions = {}
    vendor = Vendors.CRITICALSTART

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
