from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class VectraAiTemplate(Template):
    id = "vectra_ai"
    name = "Vectra AI"
    category = Template.Category.SIEM
    versions = {}
    vendor = Vendors.VECTRA_AI

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
