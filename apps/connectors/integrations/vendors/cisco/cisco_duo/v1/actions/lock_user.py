from apps.connectors.integrations.actions.user import (
    <PERSON><PERSON><PERSON>,
    User<PERSON>ock<PERSON><PERSON><PERSON>,
    UserLockStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.api import CiscoDuoV1Api
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllHosts,  # We'll create a proper user management health check later
)


class CiscoDuoV1LockUser(LockUser):
    """
    Lock a user account in Cisco Duo.

    This action uses the Duo Admin API to set a user's status to 'locked_out'.
    Reference: https://duo.com/docs/adminapi#modify-user
    """

    def execute(self, args: UserIdentifierArgs) -> UserLockResult:
        """
        Execute the lock user action.

        Args:
            args: UserIdentifierArgs containing the user_id to lock

        Returns:
            UserLockResult: Result indicating whether the user was locked
        """
        api: CiscoDuoV1Api = self.integration.get_api()

        # Call the Duo API to lock the user
        response = api.modify_user(args.user_id.value, status="locked_out")

        # Check if the operation was successful
        if response.get("stat") == "OK":
            return UserLockResult(result=UserLockStatus(locked=True))
        else:
            error_message = response.get("message", "Failed to lock user")
            return UserLockResult(
                error=ErrorDetail(message=f"Failed to lock user: {error_message}")
            )

    def get_permission_checks(self):  # pragma: no cover
        return [ReadAllHosts]
