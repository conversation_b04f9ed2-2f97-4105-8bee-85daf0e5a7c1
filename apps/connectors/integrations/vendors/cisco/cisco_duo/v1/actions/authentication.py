from apps.connectors.integrations.schemas import ocsf


def map_factor_type(factor: str) -> ocsf.AuthenticationFactorType:
    """Map Cisco Duo factor to OCSF AuthenticationFactorType."""
    factor_mapping = {
        # Push notification factors
        "duo_push": ocsf.AuthenticationFactorType.PUSH_NOTIFICATION,
        "verified_duo_push": ocsf.AuthenticationFactorType.PUSH_NOTIFICATION,
        "duo push (passwordless)": ocsf.AuthenticationFactorType.PUSH_NOTIFICATION,
        "verified duo push (passwordless)": ocsf.AuthenticationFactorType.PUSH_NOTIFICATION,
        # OTP factors
        "duo_mobile_passcode": ocsf.AuthenticationFactorType.OTP,
        "duo_mobile_passcode_hotp": ocsf.AuthenticationFactorType.OTP,
        "duo_mobile_passcode_totp": ocsf.AuthenticationFactorType.OTP,
        "yubikey_code": ocsf.AuthenticationFactorType.OTP,
        "desktop authenticator": ocsf.AuthenticationFactorType.OTP,
        # Hardware token
        "hardware_token": ocsf.AuthenticationFactorType.HARDWARE_TOKEN,
        # SMS factors
        "sms_passcode": ocsf.AuthenticationFactorType.SMS,
        "sms_refresh": ocsf.AuthenticationFactorType.SMS,
        # Phone call
        "phone_call": ocsf.AuthenticationFactorType.PHONE_CALL,
        # WebAuthn factors
        "webauthn_credential": ocsf.AuthenticationFactorType.WEBAUTHN,
        "webauthn_security_key": ocsf.AuthenticationFactorType.WEBAUTHN,
        "webauthn chrome touch id": ocsf.AuthenticationFactorType.WEBAUTHN,
        # Unknown/unavailable
        "not_available": ocsf.AuthenticationFactorType.UNKNOWN,
    }
    return factor_mapping.get(factor.lower(), ocsf.AuthenticationFactorType.OTHER)


def map_status_and_disposition(
    result: str, reason: str
) -> tuple[ocsf.EventStatus, ocsf.Disposition]:
    """Map Cisco Duo result and reason to OCSF status and disposition."""
    if result == "success":
        # All success reasons map to ALLOWED disposition
        return ocsf.EventStatus.SUCCESS, ocsf.Disposition.ALLOWED
    elif result == "denied":
        # Map specific denial reasons to dispositions based on documentation
        reason_mapping = {
            # Alert reasons
            "user_marked_fraud": ocsf.Disposition.ALERT,
            # Unauthorized reasons
            "deny_unenrolled_user": ocsf.Disposition.UNAUTHORIZED,
            "invalid_passcode": ocsf.Disposition.UNAUTHORIZED,
            "denied_by_policy": ocsf.Disposition.UNAUTHORIZED,
            "no_duo_certificate_present": ocsf.Disposition.UNAUTHORIZED,
            "user_provided_invalid_certificate": ocsf.Disposition.UNAUTHORIZED,
            "could_not_determine_if_endpoint_was_trusted": ocsf.Disposition.UNAUTHORIZED,
            "invalid_management_certificate_collection_state": ocsf.Disposition.UNAUTHORIZED,
            "no_referring_hostname_provided": ocsf.Disposition.UNAUTHORIZED,
            "invalid_referring_hostname_provided": ocsf.Disposition.UNAUTHORIZED,
            "no_web_referer_match": ocsf.Disposition.UNAUTHORIZED,
            "endpoint_failed_google_verification": ocsf.Disposition.UNAUTHORIZED,
            "endpoint_is_not_trusted": ocsf.Disposition.UNAUTHORIZED,
            "endpoint_is_not_in_management_system": ocsf.Disposition.UNAUTHORIZED,
            "no_activated_duo_mobile_account": ocsf.Disposition.UNAUTHORIZED,
            "verification_code_incorrect": ocsf.Disposition.UNAUTHORIZED,
            # Error reasons
            "error": ocsf.Disposition.ERROR,
            # Delayed reasons
            "locked_out": ocsf.Disposition.DELAYED,
            "queued_inflight_auth_expired": ocsf.Disposition.DELAYED,
            # Access revoked reasons
            "user_disabled": ocsf.Disposition.ACCESS_REVOKED,
            # Dropped reasons
            "user_cancelled": ocsf.Disposition.DROPPED,
            # No action reasons
            "no_response": ocsf.Disposition.NO_ACTION,
            "no_keys_pressed": ocsf.Disposition.NO_ACTION,
            "call_timed_out": ocsf.Disposition.NO_ACTION,
            # Blocked reasons
            "location_restricted": ocsf.Disposition.BLOCKED,
            "software_restricted": ocsf.Disposition.BLOCKED,
            "frequent_attempts": ocsf.Disposition.BLOCKED,
            # Rejected reasons
            "factor_restricted": ocsf.Disposition.REJECTED,
            "platform_restricted": ocsf.Disposition.REJECTED,
            "version_restricted": ocsf.Disposition.REJECTED,
            "rooted_device": ocsf.Disposition.REJECTED,
            "no_screen_lock": ocsf.Disposition.REJECTED,
            "touch_id_disabled": ocsf.Disposition.REJECTED,
            "no_disk_encryption": ocsf.Disposition.REJECTED,
            "anonymous_ip": ocsf.Disposition.REJECTED,
            "out_of_date": ocsf.Disposition.REJECTED,
            "invalid_device": ocsf.Disposition.REJECTED,
            "verification_code_missing": ocsf.Disposition.REJECTED,
        }
        return ocsf.EventStatus.FAILURE, reason_mapping.get(
            reason, ocsf.Disposition.BLOCKED
        )
    elif result == "error":
        return ocsf.EventStatus.FAILURE, ocsf.Disposition.ERROR
    elif result == "fraud":
        return ocsf.EventStatus.FAILURE, ocsf.Disposition.ALERT
    else:
        return ocsf.EventStatus.UNKNOWN, ocsf.Disposition.UNKNOWN


def normalize_authentication_log(log: dict) -> ocsf.Authentication:
    """
    Normalize a Cisco Duo authentication log to OCSF Authentication format.

    Based on the detailed field mapping in Cisco_Duo_Data_Integration_Documentation.md
    """
    access_device = log.get("access_device", {})
    auth_device = log.get("auth_device", {})
    user_info = log.get("user", {})
    application = log.get("application", {})
    location = access_device.get("location", {})
    auth_device_location = auth_device.get("location", {})

    # Map status and disposition
    result = log.get("result", "")
    reason = log.get("reason", "")
    status, disposition = map_status_and_disposition(result, reason)

    # Map authentication factor
    factor = log.get("factor", "")
    factor_type = map_factor_type(factor)

    # Determine activity type based on event_type
    event_type = log.get("event_type", "")
    activity = (
        ocsf.AuthenticationActivity.LOGON
        if event_type == "authentication"
        else ocsf.AuthenticationActivity.PREAUTH
    )

    # Build authentication factor with additional details
    auth_factors = []
    if factor:
        auth_factor = ocsf.AuthenticationFactor(
            factor_type=factor_type,
            provider="Cisco Duo",
        )

        auth_factors.append(auth_factor)

    # Build security agents list from access device
    agents_list = []
    security_agents = access_device.get("security_agents", [])
    for agent in security_agents:
        if isinstance(agent, dict):
            agents_list.append(
                ocsf.Agent(
                    name=agent.get("security_agent"),
                    version=agent.get("version"),
                    type_id=1,  # Endpoint Detection and Response
                )
            )

    # Build source endpoint with comprehensive mapping
    src_endpoint = None
    if access_device:
        src_endpoint = ocsf.NetworkEndpoint(
            ip=access_device.get("ip"),
            hostname=access_device.get("hostname"),
            uid=access_device.get("epkey"),
            location=ocsf.GeoLocation(
                city=location.get("city"),
                country=location.get("country"),
                region=location.get("state"),
                lat=location.get("latitude"),
                long=location.get("longitude"),
            )
            if location
            else None,
            os=ocsf.OperatingSystem(
                name=access_device.get("os"),
                version=access_device.get("os_version"),
            )
            if access_device.get("os")
            else None,
            agent_list=agents_list if agents_list else None,
        )

    # Build authentication device
    device = None
    if auth_device:
        device = ocsf.Device(
            name=auth_device.get("name"),
            uid=auth_device.get("key"),
            ip=auth_device.get("ip"),
            location=ocsf.GeoLocation(
                city=auth_device_location.get("city"),
                country=auth_device_location.get("country"),
                region=auth_device_location.get("state"),
                lat=auth_device_location.get("latitude"),
                long=auth_device_location.get("longitude"),
            )
            if auth_device_location
            else None,
        )

    # Build HTTP request with user agent
    http_request = None
    if access_device.get("browser"):
        user_agent = access_device.get("browser", "")
        http_request = ocsf.HttpRequest(user_agent=user_agent)

    # Create a descriptive message for the authentication event
    user_name = user_info.get("name", "Unknown User")
    app_name = application.get("name", "Unknown Application")
    result_text = result.replace("_", " ").title()
    message = f"Cisco Duo authentication {result_text.lower()} for user {user_name} accessing {app_name}"

    return ocsf.Authentication(
        activity=activity,
        message=message,  # Required field for OCSF BaseEvent
        time_dt=log.get("isotimestamp"),
        time=int(log.get("timestamp", 0)),
        isotimestamp=log.get("isotimestamp"),  # Preserve original isotimestamp
        metadata=ocsf.Metadata(
            uid=log.get("txid"),
            correlation_uid=log.get("txid"),  # Use txid for correlation
            event_code="authentication",  # Required field for OCSF Metadata
            product=ocsf.Product(
                name="Cisco Duo",
                vendor_name="Cisco",
            ),
            profiles=["cloud", "datetime"],
        ),
        actor=ocsf.Actor(
            user=ocsf.User(
                name=user_info.get("name"),
                uid=user_info.get("key"),
                uid_alt=log.get("alias") if log.get("alias") else None,
                email_addr=log.get("email"),
                groups=[
                    ocsf.Group(name=group) for group in user_info.get("groups", [])
                ],
            ),
        ),
        src_endpoint=src_endpoint,
        device=device,
        service=ocsf.Service(
            name=application.get("name"),
            uid=application.get("key"),
        )
        if application
        else None,
        auth_factors=auth_factors,
        status=status,
        disposition=disposition,
        status_detail=reason,
        http_request=http_request,
    )
