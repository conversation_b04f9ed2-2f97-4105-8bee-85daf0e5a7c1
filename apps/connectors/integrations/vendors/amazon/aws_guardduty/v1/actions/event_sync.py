import logging
from datetime import datetime
from typing import Generator

from dateutil import parser
from numpy import float32

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRefExtended,
)
from apps.connectors.integrations.schemas.ocsf import (
    Account,
    Api,
    Cloud,
    DetectionActivity,
    DetectionFinding,
    DetectionStatus,
    DNSQuery,
    EvidenceArtifacts,
    Feature,
    FindingInformation,
    GeoLocation,
    Metadata,
    NetworkConnectionInfo,
    NetworkEndpoint,
    Product,
    ResourceDetails,
    Response,
    Service,
)
from apps.connectors.integrations.vendors.amazon.aws_guardduty.v1.api import (
    AwsGuarddutyV1Api,
    paginate,
)
from apps.connectors.integrations.vendors.amazon.aws_guardduty.v1.bookmarks import (
    AwsGuarddutyV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.amazon.aws_guardduty.v1.health_check import (
    ReadAllFindings,
)

logger = logging.getLogger(__name__)


def format_datetime(dt: datetime) -> str:
    # YYYY-MM-DDTHH:MM:SSZ
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


def parse_datetime(dt_str: str) -> datetime:
    return parser.isoparse(dt_str)


def init_resource(event: dict) -> ResourceDetails:
    resource_details = ResourceDetails(
        region=event.get("AvailabilityZone"),
        cloud_partition="aws",
    )

    return resource_details


def map_severity(severity: float32) -> str:  # pragma: no cover
    if severity >= 9.0:
        return "Critical"
    elif severity >= 7.0:
        return "High"
    elif severity >= 4.0:
        return "Medium"
    else:
        return "Low"


def get_network_endpoint(action_details: dict, key: str) -> NetworkEndpoint:
    return NetworkEndpoint(
        port=action_details.get(key, {}).get("Port"),
        ip=action_details.get(key, {}).get("IpAddressV4")
        if action_details.get(key, {}).get("IpAddressV4")
        else action_details.get(key, {}).get("IpAddressV6"),
        location=GeoLocation(
            city=action_details.get(key, {}).get("City", {}).get("CityName"),
            country=action_details.get(key, {}).get("Country", {}).get("CountryName"),
            lat=action_details.get(key, {}).get("GeoLocation", {}).get("Lat"),
            lng=action_details.get(key, {}).get("GeoLocation", {}).get("Lng"),
        ),
    )


def convert_to_ocsf(
    event: dict,
) -> DetectionFinding:
    resources = []
    event_resources = event.get("Resource", {})
    for resource in event_resources:
        resource_obj = event_resources[resource]
        resource_details = None
        match resource:
            case "AccessKeyDetails":
                resource_details = init_resource(event)
                resource_details.type = "AwsIamAccessKey"
                resource_details.uid = resource_obj.get("AccessKeyId")
                resource_details.data = {
                    "AwsIamAccessKey": {
                        "PrincipalId": resource_obj.get("PrincipalId"),
                        "PrincipalName": resource_obj.get("UserName"),
                        "PrincipalType": resource_obj.get("IAMUser"),
                    }
                }
                resources.append(resource_details)
            # resource_details.
            case "InstanceDetails":
                resource_details = init_resource(event)
                resource_details.type = "AwsEc2Instance"
                resource_details.uid = resource_obj.get("InstanceId")
                network_interface = resource_obj.get("NetworkInterface", {})
                resource_details.data = {
                    "AwsEc2Instance": {
                        "Type": resource_obj.get("InstanceType"),
                        "VpcId": network_interface.get("VpcId"),
                        "ImageId": resource_obj.get("ImageId"),
                        "IpV4Addresses": [
                            network_interface.get("PrivateIpAddress"),
                            network_interface.get("PublicIp"),
                        ],
                        "LaunchedAt": resource_obj.get("LaunchTime"),
                        "IamInstanceProfileArn": resource_obj.get(
                            "IamInstanceProfile", {}
                        ).get("Arn"),
                        "SubnetId": network_interface.get("SubnetId"),
                    }
                }
                resources.append(resource_details)

            case "S3BucketDetails":
                for s3_bucket_detail in resource_obj:
                    resource_details = init_resource(event)
                    resource_details.type = "AwsS3Bucket"
                    resource_details.uid = s3_bucket_detail.get("Arn")
                    resource_details.data = {
                        "AwsS3Bucket": {
                            "OwnerId": s3_bucket_detail.get("Owner", {}).get("Id"),
                            "CreatedAt": s3_bucket_detail.get("CreatedAt"),
                            "ServerSideEncryptionConfiguration": {
                                "Rules": [
                                    {
                                        "ApplyServerSideEncryptionByDefault": {
                                            "EncryptionType": s3_bucket_detail.get(
                                                "DefaultServerSideEncryption", {}
                                            ).get("EncryptionType"),
                                            "KMSMasterKeyID": s3_bucket_detail.get(
                                                "DefaultServerSideEncryption", {}
                                            ).get("KmsMasterKeyArn"),
                                        }
                                    }
                                ]
                            },
                        }
                    }
                    resources.append(resource_details)
            case "EksClusterDetails":
                resource_details = init_resource(event)
                resource_details.type = "AwsEksCluster"
                resource_details.uid = resource_obj.get("Arn")

                resource_details.data = {
                    "AwsEksCluster": {
                        "Arn": resource_obj.get("Arn"),
                        "ClusterStatus": resource_obj.get("Status"),
                        "Name": resource_obj.get("Name"),
                        "Labels": resource_obj.get("Tags"),
                    }
                }
                resources.append(resource_details)
            case "EcsClusterDetails":
                resource_details = init_resource(event)
                resource_details.type = "AwsClusterDetails"
                resource_details.uid = resource_obj.get("ClusterName")
                resource_details.data = {
                    "Status": resource_obj.get("Status"),
                    "Name": resource_obj.get("Name"),
                    "Arn": resource_obj.get("Arn"),
                    "ActiveServicesCount": resource_obj.get("ActiveServicesCount"),
                    "RegisteredContainerInstancesCount": resource_obj.get(
                        "RegisteredContainerInstancesCount"
                    ),
                    "RunningTasksCount": resource_obj.get("RunningTasksCount"),
                }
                resources.append(resource_details)
                if "TaskDetails" in resource_obj:
                    task_details = resource_obj.get("TaskDetails")
                    resource_details = init_resource(event)
                    resource_details.type = "AwsEcsTask"
                    resource_details.uid = task_details.get("Arn")

                    resource_details.data = {
                        "Arn": task_details.get("Arn"),
                        "DefinitionArn": task_details.get("DefinitionArn"),
                        "Version": task_details.get("Version"),
                        "TaskCreatedAt": task_details.get("TaskCreatedAt"),
                        "TaskStartedAt": task_details.get("TaskStartedAt"),
                        "StartedBy": task_details.get("StartedBy"),
                        "Volumes": task_details.get("Volumes"),
                        "Group": task_details.get("Group"),
                        "LaunchType": task_details.get("LaunchType"),
                    }
                    resources.append(resource_details)
                    if "Containers" in task_details:
                        for container_details in task_details.get("Containers"):
                            resource_details = init_resource(event)
                            resource_details.type = "AwsEcsContainer"
                            resource_details.uid = container_details.get("Id")
                            resource_details.data = {
                                "Container": {
                                    "ContainerRuntime": container_details.get(
                                        "ContainerRuntime"
                                    ),
                                    "ImageName": container_details.get("Image"),
                                    "Privileged": container_details.get(
                                        "SecurityContext", {}
                                    ).get("Privileged"),
                                    "Name": container_details.get("Name"),
                                }
                            }
                            resources.append(resource_details)

            case "ContainerDetails":
                resource_details = init_resource(event)
                resource_details.type = "Container"
                resource_details.uid = resource_obj.get("id")
                resource_details.data = {
                    "Container": {
                        "ContainerRuntime": resource_obj.get("ContainerRuntime"),
                        "ImageName": resource_obj.get("Image"),
                        "Privileged": resource_obj.get("SecurityContext", {}).get(
                            "Privileged"
                        ),
                        "Name": resource_obj.get("Name"),
                    }
                }
            case "RdsDbInstanceDetails":
                resource_details = init_resource(event)
                resource_details.type = "AwsRdsDbInstance"
                resource_details.uid = resource_obj.get("DbInstanceArn")
                resource_details.data = {
                    "AwsRdsDbInstance": {
                        "EngineVersion": resource_obj.get("EngineVersion"),
                        "DBClusterIdentifier": resource_obj.get("DBClusterIdentifier"),
                        "DBInstanceIdentifier": resource_obj.get(
                            "DBInstanceIdentifier"
                        ),
                        "Engine": resource_obj.get("Engine"),
                        "DBInstanceClass": resource_obj.get("DBInstanceClass"),
                    }
                }
                resources.append(resource_details)
            case "RdsLimitlessDbDetails":
                resource_details = init_resource(event)
                resource_details.type = "AwsRdsDbCluster"
                resource_details.uid = resource_obj.get("DbClusterIdentifier")
                resource_details.data = {
                    "AwsRdsDbCluster": {
                        "EngineVersion": resource_obj.get("EngineVersion"),
                        "DBClusterIdentifier": resource_obj.get("DBClusterIdentifier"),
                        "Engine": resource_obj.get("Engine"),
                        "DbShardGroupIdentifier": resource_obj.get(
                            "DbShardGroupIdentifier"
                        ),
                        "DbShardGroupResourceId": resource_obj.get(
                            "DbShardGroupResourceId"
                        ),
                        "DbShardGroupArn": resource_obj.get("DbShardGroupArn"),
                    }
                }
                resources.append(resource_details)

            case "LambdaDetails":
                resource_details = init_resource(event)
                resource_details.type = "AwsLambdaFunction"
                resource_details.uid = resource_obj.get("FunctionArn")
                resource_details.data = {
                    "AwsLambdaFunction": {
                        "LastModified": resource_obj.get("LastModified"),
                        "Role": resource_obj.get("Role"),
                        "FunctionName": resource_obj.get("FunctionName"),
                        "VpcConfig": {
                            "VpcId": resource_obj.get("VpcConfig", {}).get("VpcId"),
                            "SecurityGroupIds": [
                                group_id
                                for group_id in resource_obj.get("VpcConfig", {}).get(
                                    "SecurityGroups", []
                                )
                            ],
                            "SubnetIds": resource_obj.get("VpcConfig", {}).get(
                                "SubnetIds"
                            ),
                        },
                        "Version": resource_obj.get("Version"),
                        "RevisionId": resource_obj.get("RevisionId"),
                    }
                }
                resources.append(resource_details)
            case "S3ObjectDetails":  # pragma: no cover
                pass

    evidences = [
        EvidenceArtifacts(
            data={
                "ThreatIntelligenceDetails": event.get("Service", {})
                .get("Evidence", {})
                .get("ThreatIntelligenceDetails")
            },
        )
    ]
    for action in event.get("Service", {}).get("Action", {}):
        action_details = event.get("Service", {}).get("Action", {}).get(action)
        match action:
            case "NetworkConnectionAction":
                evidence_obj = EvidenceArtifacts(
                    connection_info=NetworkConnectionInfo(
                        direction=action_details.get("ConnectionDirection")
                        .lower()
                        .capitalize(),
                        protocol_name=action_details.get("Protocol"),
                    ),
                    dst_endpoint=get_network_endpoint(action_details, "LocalIpDetails"),
                    src_endpoint=get_network_endpoint(
                        action_details, "RemoteIpDetails"
                    ),
                )

                evidences.append(evidence_obj)
            case "DnsRequestAction":
                evidence_obj = EvidenceArtifacts(
                    connection_info=NetworkConnectionInfo(
                        direction="Outbound",
                        protocol_name=action_details.get("Protocol"),
                    ),
                    query=DNSQuery(hostname=action_details.get("DomainWithSuffix")),
                )
                evidences.append(evidence_obj)
            case "AwsApiCallAction":
                evidence_obj = EvidenceArtifacts(
                    api=Api(
                        operation=action_details.get("Api"),
                        response=Response(
                            error=action_details.get("ErrorCode"),
                        ),
                        service=Service(
                            name=action_details.get("ServiceName"),
                        ),
                    ),
                    src_endpoint=get_network_endpoint(
                        action_details, "RemoteIpDetails"
                    ),
                )
                evidences.append(evidence_obj)
            case "KubernetesApiCallAction":
                evidence_obj = EvidenceArtifacts(
                    api=Api(
                        operation=action_details.get("Api"),
                        response=Response(
                            code=action_details.get("StatusCode"),
                        ),
                    ),
                    src_endpoint=get_network_endpoint(
                        action_details, "RemoteIpDetails"
                    ),
                )
                evidences.append(evidence_obj)
            case "RdsLoginAttemptAction":
                evidence_obj = EvidenceArtifacts(
                    src_endpoint=get_network_endpoint(action_details, "RemoteIpDetails")
                )
                evidences.append(evidence_obj)

    return DetectionFinding(
        activity=DetectionActivity.UNKNOWN,
        message=event.get("Title"),
        cloud=Cloud(
            account=Account(
                uid=event.get("AccountId"),
            ),
            region=event.get("Region"),
            provider="AWS",
        ),
        finding_info=FindingInformation(
            uid=event.get("Arn"),
            created_time_dt=parse_datetime(event.get("CreatedAt")),
            types=[
                event.get("Type"),
            ],
            modified_time_dt=parse_datetime(event.get("UpdatedAt")),
            desc=event.get("Title"),
        ),
        metadata=Metadata(
            product=Product(
                feature=Feature(
                    uid=event.get("Arn").split("/finding")[0],
                )
            ),
            event_code=None,
            correlation_uid=event.get("Arn"),
            profiles=[],
        ),
        severity=map_severity(event.get("Severity")),
        time_dt=parse_datetime(event.get("UpdatedAt")),
        evidences=evidences,
        status=DetectionStatus.ARCHIVED
        if event.get("Service", {}).get("Archived")
        else DetectionStatus.NEW,
        resources=resources,
    )


def normalize_event(event: dict) -> Event:
    finding = event.get("finding")
    detector_id = event.get("detector_id")
    return Event(
        raw_event=finding,
        ioc=EventIOCInfo(
            external_id=finding.get("Type"),
            external_name=finding.get("Title"),
            has_ioc_definition=False,
            mitre_techniques=None,
        ),
        vendor_item_ref=VendorRefExtended(
            id=detector_id + ";" + finding.get("Id"),
            title=finding.get("Title"),
            url=finding.get("Arn"),
            created=finding.get("CreatedAt"),
        ),
        vendor_group_ref=None,
        event_timestamp=parse_datetime(finding.get("UpdatedAt")),
        ocsf=convert_to_ocsf(finding),
    )


class AwsGuardDutyV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: AwsGuarddutyV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: AwsGuarddutyV1Api = self.integration.get_api()
        latest_event_update_datetime = parse_datetime(
            bookmark.latest_event_update_datetime
        )
        updated_at = latest_event_update_datetime
        for detector_ids in paginate(api.list_detectors, "DetectorIds", **kwargs):
            for detector_id in detector_ids:
                for finding_ids in paginate(
                    api.list_findings,
                    "FindingIds",
                    detector_id=detector_id,
                    updated_at=updated_at,
                ):
                    findings = api.get_findings(detector_id, finding_ids)["Findings"]
                    for finding in findings:
                        if (
                            parse_datetime(finding.get("UpdatedAt"))
                            > latest_event_update_datetime
                        ):
                            latest_event_update_datetime = parse_datetime(
                                finding.get("UpdatedAt")
                            )
                        yield {"finding": finding, "detector_id": detector_id}
        bookmark.latest_event_update_datetime = format_datetime(
            latest_event_update_datetime
        )

    def get_permission_checks(self):
        return [ReadAllFindings]
