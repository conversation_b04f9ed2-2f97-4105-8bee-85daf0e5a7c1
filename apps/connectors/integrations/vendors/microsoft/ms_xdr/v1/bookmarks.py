from datetime import datetime, timedelta, timezone

from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


def default_latest_event_update_datetime():
    return (
        (datetime.now(timezone.utc) - timedelta(days=1))
        .isoformat()
        .replace("+00:00", "Z")
    )


class MsXdrV1EventSyncBookmark(TemplateVersionActionBookmark):
    latest_alert_update_datetime: str = Field(
        title="Latest Alert Update Datetime",
        description="The latest lastUpdateDateTime received during a fetch.",
        default_factory=default_latest_event_update_datetime,
    )
    latest_incident_update_datetime: str = Field(
        title="Latest Incident Update Datetime",
        description="The latest lastUpdateDateTime received during a fetch.",
        default_factory=default_latest_event_update_datetime,
    )


MsXdrV1Bookmarks = create_bookmarks_model(
    "MsXdrV1Bookmarks",
    {
        IntegrationActionType.EVENT_SYNC: MsXdrV1EventSyncBookmark,
    },
)
