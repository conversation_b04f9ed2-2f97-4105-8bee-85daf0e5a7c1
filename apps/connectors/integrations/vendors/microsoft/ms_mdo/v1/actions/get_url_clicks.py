from apps.connectors.integrations.actions.get_url_clicks import (
    GetUrlClicksByMessage,
    GetUrlClicksByMessageSender,
    GetUrlClicksByUrl,
)
from apps.connectors.integrations.schemas.ocsf import (
    Actor,
    Disposition,
    Metadata,
    NetworkActivity,
    NetworkActivityType,
    Profile,
    Url,
    User,
)
from apps.connectors.integrations.schemas.query_args import (
    MailMessageQueryArgs,
    UrlQueryArgs,
)
from apps.connectors.integrations.schemas.query_result import NetworkActivityQueryResult
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.utils import (
    QueryActionMixin,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ReadThreatHunting,
)


def normalize_network_activity(src: dict) -> NetworkActivity:
    return NetworkActivity(
        metadata=Metadata(
            correlation_uid=src.get("ReportId"),
            event_code=src.get("Type"),
            profiles=[
                Profile.DATETIME,
            ],
        ),
        activity=NetworkActivityType.OTHER,
        time_dt=src["Timestamp"],
        app_name=src["Workload"],
        actor=Actor(
            user=User(
                uid_alt=src["AccountUpn"],
            )
        ),
        url=Url(
            url_string=src["Url"],
        ),
        message=src.get("ActionType"),
        disposition=Disposition.ALLOWED
        if src["ActionType"] == "ClickAllowed"
        else Disposition.BLOCKED,
    )


class MsMdoV1GetUrlClicksByUrl(GetUrlClicksByUrl, QueryActionMixin):
    def execute(self, args: UrlQueryArgs, **kwargs) -> NetworkActivityQueryResult:
        raw_activities = self.execute_query(
            """
        UrlClickEvents
        | where Url == "{{ url }}"
            """,
            args,
        )
        normalized_results = [normalize_network_activity(src) for src in raw_activities]
        return NetworkActivityQueryResult(result=normalized_results)

    def get_permission_checks(self, *args, **kwargs):
        return [ReadThreatHunting]


class MsMdoV1GetUrlClicksByMessage(GetUrlClicksByMessage, QueryActionMixin):
    def execute(
        self, args: MailMessageQueryArgs, **kwargs
    ) -> NetworkActivityQueryResult:
        raw_activities = self.execute_query(
            """
        let messageIds = (EmailEvents
        | where NetworkMessageId == '{{ identifier.value }}' or InternetMessageId == '{{ identifier.value }}'
        | distinct NetworkMessageId);
        UrlClickEvents
        | where NetworkMessageId in (messageIds)
            """,
            args,
        )
        normalized_results = [normalize_network_activity(src) for src in raw_activities]
        return NetworkActivityQueryResult(result=normalized_results)

    def get_permission_checks(self, *args, **kwargs):
        return [ReadThreatHunting]


class MsMdoV1GetUrlClicksByMessageSender(GetUrlClicksByMessageSender, QueryActionMixin):
    def execute(
        self, args: MailMessageQueryArgs, **kwargs
    ) -> NetworkActivityQueryResult:
        raw_activities = self.execute_query(
            """
        let senders = (EmailEvents
        | where NetworkMessageId == '{{ identifier.value }}'
        | distinct SenderMailFromAddress);
        let messageIds = (EmailEvents
        | where SenderMailFromAddress in (senders)
        | distinct NetworkMessageId);
        UrlClickEvents
        | where NetworkMessageId in (messageIds)
            """,
            args,
        )
        normalized_results = [normalize_network_activity(src) for src in raw_activities]
        return NetworkActivityQueryResult(result=normalized_results)

    def get_permission_checks(self, *args, **kwargs):
        return [ReadThreatHunting]
