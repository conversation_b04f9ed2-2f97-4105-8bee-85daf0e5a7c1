from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class AzureDevOpsAuditingTemplate(Template):
    id = "azure_devops_auditing"
    name = "Microsoft Azure DevOps Auditing"
    category = Template.Category.IT_OPERATIONS
    versions = {}
    vendor = Vendors.MICROSOFT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
