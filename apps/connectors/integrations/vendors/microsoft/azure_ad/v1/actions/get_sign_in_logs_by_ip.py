from apps.connectors.integrations.actions.user.get_sign_in_logs import (
    GetSignInLogsByIp,
    SignInLogsByIpArgs,
    SignInLogsResult,
)
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    AuditLogReadAll,
)

from .authentication import normalize_sign_in


class AzureAdV1GetSignInLogsByIp(GetSignInLogsByIp):
    def execute(self, args: SignInLogsByIpArgs) -> SignInLogsResult:
        api = self.integration.get_api()
        api.client.URL = api.client.BETA
        params = {
            "$filter": f"createdDateTime ge {args.start_time} and createdDateTime le {args.end_time} and ipAddress eq '{args.ip_address.value}'"
        }
        sign_ins = api.get_sign_in_logs(params=params)

        normalized_sign_ins = [
            normalize_sign_in(sign_in) for sign_in in sign_ins["value"]
        ]
        return SignInLogsResult(result=normalized_sign_ins)

    def get_permission_checks(self, *args, **kwargs):
        return [AuditLogReadAll]
