from apps.connectors.integrations.actions.user.get_sign_in_logs import (
    GetSignInLogsByUPN,
    GetSignInLogsByUser,
    SignInLogsByUPNArgs,
    SignInLogsByUserIdArgs,
    SignInLogsResult,
)
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    AuditLogReadAll,
)

from .authentication import normalize_sign_in


def execute(api, start_time, end_time, user_filter):
    api.client.URL = api.client.BETA
    params = {
        "$filter": f"createdDateTime ge {start_time} and createdDateTime le {end_time} and {user_filter}"
    }
    sign_ins = api.get_sign_in_logs(params=params)

    normalized_sign_ins = [normalize_sign_in(sign_in) for sign_in in sign_ins["value"]]
    return SignInLogsResult(result=normalized_sign_ins)


class AzureAdV1GetSignInLogsByUser(GetSignInLogsByUser):
    def execute(self, args: SignInLogsByUserIdArgs) -> SignInLogsResult:
        api = self.integration.get_api()
        user_filter = f"userId eq '{args.user_id.value}'"

        return execute(
            api,
            start_time=args.start_time,
            end_time=args.end_time,
            user_filter=user_filter,
        )

    def get_permission_checks(self, *args, **kwargs):
        return [AuditLogReadAll]


class AzureAdV1GetSignInLogsByUPN(GetSignInLogsByUPN):
    def execute(self, args: SignInLogsByUPNArgs) -> SignInLogsResult:
        api = self.integration.get_api()
        user_filter = f"userPrincipalName eq '{args.upn.value}'"

        return execute(
            api,
            start_time=args.start_time,
            end_time=args.end_time,
            user_filter=user_filter,
        )

    def get_permission_checks(self, *args, **kwargs):
        return [AuditLogReadAll]
