from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class AzureStorageAnalyticsTemplate(Template):
    id = "azure_storage_analytics"
    name = "Microsoft Azure Storage Analytics"
    category = Template.Category.IT_OPERATIONS
    versions = {}
    vendor = Vendors.MICROSOFT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
