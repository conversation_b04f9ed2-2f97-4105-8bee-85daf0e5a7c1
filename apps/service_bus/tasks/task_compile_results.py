import logging

from celery import shared_task

from apps import tracing
from apps.connectors.models import Connector
from apps.connectors.services import artifact_service, connector_service
from apps.service_bus.v1.schemas.integration import (
    IntegrationInvokeActionCommand,
    IntegrationInvokeCommandResponse,
)
from core.celery import BaseTask

logger = logging.getLogger(__name__)


class ConnectorDisabledError(Exception):
    pass


class ActionNotSupportedError(Exception):
    pass


class ConnectorUnhealthyError(Exception):
    pass


class CompileResultsTask(BaseTask):
    throws = (ConnectorUnhealthyError,)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        if isinstance(exc, ConnectorUnhealthyError):
            status = "UNHEALTHY"
        elif isinstance(exc, ConnectorDisabledError):
            status = "DISABLED"
        else:
            status = "FAILURE"
            super().on_failure(exc, task_id, args, kwargs, einfo)

        from core.service_bus import service_bus

        response = IntegrationInvokeCommandResponse(status=status, artifact_ids=None)

        correlation_id, sender_id, command, *_ = args
        service_bus.send_command_response(
            command=command,
            command_response_body=response,
            correlation_id=correlation_id,
            sender_id=sender_id,
        )

    def on_success(self, retval, task_id, args, kwargs):
        super().on_success(retval, task_id, args, kwargs)

        artifact_ids, artifact_keys, return_type = retval
        response = IntegrationInvokeCommandResponse(
            status="SUCCESS",
            artifact_ids=artifact_ids,
            artifact_keys=artifact_keys,
            artifact_filesystem_url=artifact_service.get_file_system_url(),
            return_type=return_type,
        )

        # TODO: decide if we can/should pass some kind of partition key here
        from core.service_bus import service_bus

        correlation_id, sender_id, command, *_ = args
        service_bus.send_command_response(
            command=command,
            command_response_body=response,
            correlation_id=correlation_id,
            sender_id=sender_id,
        )


@shared_task(base=CompileResultsTask)
def compile_results(correlation_id, sender_id, command, command_body):
    """Invoke a connector method."""
    request = IntegrationInvokeActionCommand.model_validate_json(command_body)

    connector_id = request.integration_id
    connector = Connector.objects.get(id=connector_id)

    tracing.set_context(
        org_alias=connector.organization.alias,
        technology_id=connector.technology_id,
        connector_id=connector.id,
    )

    if not connector.enabled:
        logger.warning("Connector is disabled", extra=connector.log_extra)
        raise ConnectorDisabledError()

    if request.action not in connector.enabled_actions:
        logger.warning(
            "Connector does not support the requested action",
            extra={**connector.log_extra, "action": request.action},
        )
        raise ActionNotSupportedError()

    if not connector_service.is_action_healthy(connector, request.action):
        logger.warning(
            "Connector is unhealthy for action",
            extra={**connector.log_extra, "action": request.action},
        )
        raise ConnectorUnhealthyError()

    integration = connector.get_integration()

    result_generator = integration.invoke_action(request.action, **request.action_args)
    result_type = integration.get_action_result_type_name(request.action)

    art_key = artifact_service.build_key(
        connector.organization_id,
        connector.technology_id,
        connector.version_id,
        connector.id,
        correlation_id,
    )

    # We only have one artifact per task for now. We anticipate
    # that changing in the future and are returning a list of
    # so that we can easily add more artifacts later.
    artifact_ids = []
    artifact_keys = []
    artifact_keys.append(art_key)
    artifact_id = artifact_service.convert_key_to_id(art_key)
    artifact_ids.append(artifact_id)

    artifact_service.write_results(art_key, result_generator)

    connector.update_last_activity_at()

    return artifact_ids, artifact_keys, result_type
