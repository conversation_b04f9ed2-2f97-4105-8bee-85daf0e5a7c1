from apps.connectors.integrations import Template
from apps.demo.integrations.template import DemoTemplate

from .v1 import DemoNozomiV1TemplateVersion


class DemoNozomiTemplate(DemoTemplate):
    id = "nozomi"
    name = "Nozomi Networks"
    category = Template.Category.OT_SECURITY
    versions = {
        DemoNozomiV1TemplateVersion.id: DemoNozomiV1TemplateVersion(),
    }
    vendor = "Nozomi Networks"
