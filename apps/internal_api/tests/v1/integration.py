from datetime import datetime
from http import HTTPStatus
from uuid import uuid4

import responses
from django.utils import timezone
from fastapi import status

from apps.connectors.models import Connector
from apps.connectors.tests.integrations.azure_ad_v1 import setup_oauth_responses
from factories import AadAppFactory, ConnectorFactory, OrganizationFactory
from factories.connection import ConnectionFactory

from .base import BaseInternalApiTestCase


class IntegrationTests(BaseInternalApiTestCase):
    def setUp(self):
        super().setUp()

        self._patch_encryption()

        self.organization = OrganizationFactory()

        self.connector = ConnectorFactory(
            organization=self.organization,
            technology_id="sentinel_one",
            version_id="v2_1",
            last_activity_at=timezone.now(),
            product_id=1001,
        )

        self.connector_falcon = ConnectorFactory(
            organization=self.organization,
            technology_id="falcon_em",
            version_id="v1",
            last_activity_at=timezone.now(),
            settings={
                "host_sync": {
                    "fetch_from_last_x_days": 10,
                    "fetch_from_unauthenticated_scans": True,
                }
            },
            enabled_actions=["vendor_vulnerability_sync"],
            vulnerability_coverage_mode="enabled",
            endpoint_coverage_mode="n/a",
        )

        self.connector_tenable_io = ConnectorFactory(
            organization=self.organization,
            technology_id="tenable_io",
            version_id="v1",
            last_activity_at=timezone.now(),
            settings={
                "host_sync": {
                    "fetch_from_last_x_days": 10,
                    "fetch_from_unauthenticated_scans": True,
                }
            },
            enabled_actions=[
                "detected_vulnerability_sync",
                "vendor_vulnerability_sync",
                "vulnerability_intelligence_sync",
            ],
            vulnerability_coverage_mode="enabled",
            endpoint_coverage_mode="n/a",
        )

    def test_get_integrations(self):
        response = self.client.get(self._integrations_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 3)
        integration_summary = min(resp, key=lambda x: x["technology_id"])
        self.assertEqual(integration_summary["technology_id"], "falcon_em")
        self.assertEqual(integration_summary["version_id"], "v1")
        self.assertEqual(integration_summary["category_id"], "asset_source")
        self.assertFalse(integration_summary["internal"])
        self.assertEqual(
            self.parse_datetime(integration_summary["last_activity_at"]),
            self.connector_falcon.last_activity_at,
        )
        self.assertEqual(
            integration_summary["account_id"],
            self.connector_falcon.account_id,
        )

    def test_get_integrations_with_technology_id(self):
        response = self.client.get(
            self._integrations_url() + "?technology_id=sentinel_one"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        integration_summary = next(
            (x for x in resp if x["technology_id"] == "sentinel_one"), None
        )
        self.assertEqual(integration_summary["technology_id"], "sentinel_one")

    def test_get_integrations_with_enabled_actions(self):
        response = self.client.get(
            self._integrations_url() + "?enabled_action=detected_vulnerability_sync"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        integration_summary = next(
            (x for x in resp if x["technology_id"] == "tenable_io"), None
        )
        self.assertEqual(integration_summary["technology_id"], "tenable_io")

    def test_get_integrations_with_multiple_enabled_actions(self):
        response = self.client.get(
            self._integrations_url()
            + "?enabled_action=vendor_vulnerability_sync&enabled_action=detected_vulnerability_sync"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)
        integration_summary = next(
            (x for x in resp if x["technology_id"] == "tenable_io"), None
        )
        self.assertEqual(integration_summary["technology_id"], "tenable_io")
        integration_summary = next(
            (x for x in resp if x["technology_id"] == "falcon_em"), None
        )
        self.assertEqual(integration_summary["technology_id"], "falcon_em")

    def test_get_integrations_with_organizations(self):
        response = self.client.get(
            self._integrations_url() + "?organization_id=" + str(self.organization.id)
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 3)
        integration_summary = next(
            (x for x in resp if x["technology_id"] == "falcon_em"), None
        )
        self.assertEqual(integration_summary["technology_id"], "falcon_em")

    def test_get_integrations_exclude_deleted(self):
        self.connector.is_deleted = True
        self.connector.save()
        response = self.client.get(self._integrations_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)

    def test_get_integrations_exclude_orphaned(self):
        self.organization.sync_status = self.organization.SyncStatus.ORPHANED
        self.organization.save()
        response = self.client.get(self._integrations_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 0)

    def test_get_integration(self):
        response = self.client.get(
            self._integrations_url() + "/" + str(self.connector.id)
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "sentinel_one")
        self.assertEqual(integration["version_id"], "v2_1")
        self.assertEqual(integration["category_id"], "endpoint_security")
        self.assertEqual(integration["config"]["api_key"], "**********")
        self.assertEqual(integration["settings"], {})
        self.assertFalse(integration["internal"])
        self.assertEqual(
            self.parse_datetime(integration["last_activity_at"]),
            self.connector.last_activity_at,
        )
        self.assertEqual(
            integration["account_id"],
            self.connector.account_id,
        )

    def test_get_integration_internal(self):
        self.internal_connector = ConnectorFactory(
            organization=self.organization,
            technology_id="azure_ad",
            version_id="v1",
            last_activity_at=timezone.now(),
            internal=True,
        )

        response = self.client.get(
            self._integrations_url() + "/" + str(self.internal_connector.id)
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "azure_ad")
        self.assertEqual(integration["version_id"], "v1")
        self.assertTrue(integration["internal"])
        self.assertEqual(
            integration["account_id"],
            self.connector.account_id,
        )

    def test_get_integration_not_exists(self):
        response = self.client.get(self._integrations_url() + "/" + str(uuid4()))
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_integration(self):
        integration = {
            "technology_id": "sentinel_one",
            "version_id": "v2_1",
            "config": {
                "api_key": "test_api_key",
                "url": "http://test_api_url",
            },
            # FIXME change to real settings when we add sentinel_one settings
            "settings": {},
            "organization_id": str(self.organization.id),
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "sentinel_one")
        self.assertEqual(integration["version_id"], "v2_1")
        self.assertEqual(integration["category_id"], "endpoint_security")
        self.assertEqual(integration["config"]["api_key"], "**********")
        self.assertEqual(integration["settings"], {})
        self.assertEqual(integration["enabled_actions"], ["host_sync"])
        self.assertIsNone(integration["last_activity_at"])

    def test_create_internal_integration(self):
        integration = {
            "technology_id": "sentinel_one",
            "version_id": "v2_1",
            "config": {
                "api_key": "test_api_key",
                "url": "http://test_api_url",
            },
            # FIXME change to real settings when we add sentinel_one settings
            "settings": {},
            "internal": True,
            "organization_id": str(self.organization.id),
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertTrue(integration["internal"])
        connector = Connector.objects.get(id=integration["id"])
        self.assertTrue(connector.internal)

    def test_create_integration_with_connection(self):
        aad_app = AadAppFactory()
        connection = ConnectionFactory(
            organization=self.organization,
            connection_template_id="ms_aad_app",
            config__client_id=aad_app.client_id,
        )
        integration = {
            "technology_id": "ms_xdr",
            "version_id": "v1",
            "settings": {},
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
            "connection_id": str(connection.id),
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "ms_xdr")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["connection_id"], str(connection.id))
        self.assertEqual(integration["config"]["client_id"], aad_app.client_id)

    def test_create_integration_aad_app_not_exists(self):
        client_id = str(uuid4())
        tenant_id = str(uuid4())
        integration = {
            "technology_id": "defender_atp",
            "version_id": "v1",
            "config": {
                "tenant_id": tenant_id,
                "client_id": client_id,
            },
            "settings": {
                "host_sync": {
                    "custom_filter_expression": "filter_expression",
                }
            },
            "organization_id": str(self.organization.id),
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertIn(client_id, response.json()["error"]["message"])

    def test_create_integration_aad_app_exists(self):
        aad_app = AadAppFactory()
        tenant_id = str(uuid4())
        client_id = aad_app.client_id

        integration = {
            "technology_id": "defender_atp",
            "version_id": "v1",
            "config": {
                "tenant_id": tenant_id,
                "client_id": client_id,
            },
            "settings": {},
            "organization_id": str(self.organization.id),
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        integration = response.json()
        self.assertEqual(integration["technology_id"], "defender_atp")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["category_id"], "endpoint_security")
        self.assertEqual(integration["config"]["tenant_id"], tenant_id)
        self.assertEqual(integration["config"]["client_id"], client_id)
        self.assertEqual(
            integration["settings"],
            {
                "host_sync": {
                    "fetch_inactive_machines": False,
                    "fetch_only_onboarded_machines": True,
                }
            },
        )

    def test_update_integration(self):
        integration = {
            "technology_id": "sentinel_one",
            "version_id": "v2_1",
            "config": {
                "api_key": "updated_api_key",
                "url": "http://changed_url.com",
            },
            # FIXME change to real settings when we add sentinel_one settings
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "sentinel_one")
        self.assertEqual(integration["version_id"], "v2_1")
        self.assertEqual(integration["category_id"], "endpoint_security")
        self.assertEqual(integration["config"]["api_key"], "**********")
        self.assertEqual(integration["config"]["url"], "http://changed_url.com/")
        self.assertEqual(integration["enabled_actions"], [])
        self.assertEqual(integration["settings"], {})

        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.config["api_key"], "updated_api_key")

    def test_update_integration_name(self):
        integration = self.client.get(
            self._integrations_url() + "/" + str(self.connector.id)
        ).json()

        integration["name"] = "updated_name"

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["name"], "updated_name")

        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.name, "updated_name")

    def test_update_integration_internal_status(self):
        integration = self.client.get(
            self._integrations_url() + "/" + str(self.connector.id)
        ).json()

        integration["internal"] = True

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertTrue(integration["internal"])

        connector = Connector.objects.get(id=integration["id"])
        self.assertTrue(connector.internal)

    def test_update_integration_without_name(self):
        self.connector.name = "test_name"
        self.connector.save()

        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "updated_access_key",
                "secret_key": "updated_secret_key",
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["name"], "test_name")

    def test_update_integration_no_secret_change(self):
        integration = {
            "technology_id": "sentinel_one",
            "version_id": "v2_1",
            "config": {
                "api_key": "**********",
                "url": "http://changed_url.com",
            },
            # FIXME change to real settings when we add sentinel_one settings
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "sentinel_one")
        self.assertEqual(integration["version_id"], "v2_1")
        self.assertEqual(integration["category_id"], "endpoint_security")
        self.assertEqual(integration["config"]["api_key"], "**********")
        self.assertEqual(integration["config"]["url"], "http://changed_url.com/")
        self.assertEqual(integration["settings"], {})
        self.assertEqual(integration["enabled_actions"], [])

        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.config["api_key"], "test_api_key")

    def test_delete_integration(self):
        response = self.client.delete(
            self._integrations_url() + "/" + str(self.connector.id)
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.is_deleted, True)

    def test_update_integration_from_product(self):
        """Test successful update of product_id"""
        new_product_id = 2002
        url = f"{self._integrations_url()}/{self.connector.id}/product_id/{new_product_id}"
        response = self.client.put(url)
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.assertEqual(resp["product_id"], new_product_id)
        self.assertEqual(resp["config_managed_by_portal"], False)
        self.connector.refresh_from_db()
        self.assertEqual(self.connector.product_id, new_product_id)

    @responses.activate
    def test_invoke_integration_method(self):
        self._mock_api()

        last_activity_at = self.connector.last_activity_at

        response = self.client.post(
            self._integrations_url()
            + "/"
            + str(self.connector.id)
            + "/methods/get_agents",
            json={"method_args": {}},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        self.assertEqual(
            resp["invoke_result"]["data"], [{"device_id": "test_device_id"}]
        )

        self.connector.refresh_from_db()
        self.assertGreater(self.connector.last_activity_at, last_activity_at)

    @responses.activate
    def test_invoke_integration_method_failure(self):
        self._mock_api(success=False)

        last_activity_at = self.connector.last_activity_at

        response = self.client.post(
            self._integrations_url()
            + "/"
            + str(self.connector.id)
            + "/methods/get_agents",
            json={"method_args": {}},
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.connector.refresh_from_db()
        self.assertEqual(self.connector.last_activity_at, last_activity_at)

    def _integrations_url(self):
        return "v1/integrations"

    @staticmethod
    def _mock_api(success=True):
        if success:
            responses.get(
                "https://test_url.com/web/api/v2.1/agents",
                json={
                    "data": [{"device_id": "test_device_id"}],
                    "pagination": {"nextCursor": None},
                },
            )
        else:
            responses.get(
                "https://test_url.com/web/api/v2.1/agents",
                status=500,
            )

    @staticmethod
    def parse_datetime(datetime_str):
        return datetime.strptime(datetime_str, "%Y-%m-%dT%H:%M:%S.%f%z")


class InvokeActionsTests(BaseInternalApiTestCase):
    def setUp(self):
        super().setUp()

        self._patch_encryption()

        self.organization = OrganizationFactory()

        self.aad_app = AadAppFactory()
        self.connector = ConnectorFactory(
            organization=self.organization,
            technology_id="azure_ad",
            version_id="v1",
            last_activity_at=timezone.now(),
            config__client_id=self.aad_app.client_id,
            enabled_actions=["disable_user_login", "host_sync"],
        )

        self.user_id = "test_id"

    @responses.activate
    def test_invoke_integration_with_missing_arg(self):
        self._mock_api()
        setup_oauth_responses(self.connector.config["tenant_id"])

        last_activity_at = self.connector.last_activity_at

        # Invoke action without passing requried args
        response = self.client.post(
            f"{self._integrations_url()}/{self.connector.id}/actions/disable_user_login",
            json={"value_type": "azure_ad", "value": ""},
        )

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.connector.refresh_from_db()
        self.assertEqual(self.connector.last_activity_at, last_activity_at)

    @responses.activate
    def test_invoke_integration_disabled_action(self):
        self._mock_api()
        setup_oauth_responses(self.connector.config["tenant_id"])

        last_activity_at = self.connector.last_activity_at

        # Invoke disabled action
        response = self.client.post(
            f"{self._integrations_url()}/{self.connector.id}/actions/enable_user_login",
            json={
                "user_id": {
                    "value_type": "azure_ad",
                    "value": self.user_id,
                }
            },
        )

        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        self.connector.refresh_from_db()
        self.assertEqual(self.connector.last_activity_at, last_activity_at)

    @responses.activate
    def test_invoke_integration_bad_request(self):
        setup_oauth_responses(self.connector.config["tenant_id"])
        self._mock_api(status_code=HTTPStatus.BAD_REQUEST)

        last_activity_at = self.connector.last_activity_at

        # Invoke action with invalid value
        response = self.client.post(
            f"{self._integrations_url()}/{self.connector.id}/actions/disable_user_login",
            json={
                "user_id": {
                    "value_type": "azure_ad",
                    "value": self.user_id,
                }
            },
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.connector.refresh_from_db()
        self.assertEqual(self.connector.last_activity_at, last_activity_at)

    @responses.activate
    def test_invoke_integration_action(self):
        self._mock_api()
        setup_oauth_responses(self.connector.config["tenant_id"])

        last_activity_at = self.connector.last_activity_at

        response = self.client.post(
            f"{self._integrations_url()}/{self.connector.id}/actions/disable_user_login",
            json={
                "user_id": {
                    "value_type": "azure_ad",
                    "value": self.user_id,
                }
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        result = response.json()
        self.assertFalse(result["result"]["enabled"])
        self.connector.refresh_from_db()
        self.assertGreater(self.connector.last_activity_at, last_activity_at)

    @responses.activate
    def test_invoke_integration_action_generator(self):
        self._mock_api()
        setup_oauth_responses(self.connector.config["tenant_id"])

        last_activity_at = self.connector.last_activity_at

        response = self.client.post(
            f"{self._integrations_url()}/{self.connector.id}/actions/host_sync",
            json={},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        result = response.json()
        self.assertIn("items", result)
        self.connector.refresh_from_db()
        self.assertGreater(self.connector.last_activity_at, last_activity_at)

    @responses.activate
    def test_poll_integration_action_todo(self):
        # TODO: test actual result once implemented
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="abnormal_security",
            version_id="v1",
            enabled_actions=["remediate_email_threat"],
        )

        self.client.post(
            f"{self._integrations_url()}/{connector.id}/actions/remediate_email_threat/poll",
            json={"context": {}},
        )

    def _integrations_url(self):
        return "v1/integrations"

    def _mock_api(self, status_code=HTTPStatus.NO_CONTENT):
        # Disable login
        responses.patch(
            f"https://graph.microsoft.com/v1.0/users/{self.user_id}",
            match=[responses.matchers.json_params_matcher({"accountEnabled": False})],
            status=status_code.value,
        )
        # Host Sync
        responses.get(
            f"https://graph.microsoft.com/v1.0/devices",
            json={"value": []},
            status=HTTPStatus.OK.value,
        )
