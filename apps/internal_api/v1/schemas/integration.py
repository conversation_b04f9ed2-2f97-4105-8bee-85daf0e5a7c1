from datetime import datetime
from functools import cached_property
from typing import Any, Optional
from uuid import UUID

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)

from apps.api.v1.schemas.integration import CoverageMode
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import (
    Template,
    TemplateVersionConfig,
    TemplateVersionSettings,
)


class IntegrationBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    technology_id: str = Field(frozen=True)
    version_id: str = Field(frozen=True)
    organization_id: UUID = Field(frozen=True)
    name: str = Field(default_factory=str, max_length=255)
    internal: bool = Field(default=False)
    vulnerability_coverage_mode: CoverageMode = CoverageMode.NOT_APPLICABLE
    endpoint_coverage_mode: CoverageMode = CoverageMode.NOT_APPLICABLE
    product_id: Optional[int] = Field(frozen=True, default=None)
    config_managed_by_portal: bool = Field(default=False, frozen=True)


class InternalIntegrationSummary(IntegrationBase):
    id: UUID = Field(frozen=True)
    account_id: UUID = Field(frozen=True)
    category_id: str = Field(frozen=True)
    category_name: str = Field(frozen=True)
    technology_name: str = Field(frozen=True)
    enabled: bool = Field(default=True)
    enabled_actions: list = Field(default_factory=list)
    last_activity_at: Optional[datetime] = Field(frozen=True)


class IntegrationPost(IntegrationBase):
    settings: dict[IntegrationActionType, dict] = Field(default_factory=dict)
    enabled: bool = Field(default=True)
    enabled_actions: list = Field(default_factory=list)
    # FIXME: migration_id=connection_config
    # config will no longer be accepted in this API after migration
    config: dict | None = None
    connection_id: Optional[UUID] = Field(frozen=True, default=None)

    @cached_property
    def config_model(self) -> TemplateVersionConfig:
        template = Template.get_template(self.technology_id)
        version = template.versions[self.version_id]
        return version.connection_model.config_model.model_validate(self.config)

    def validate_config(self):
        config_model = self.config_model
        self.config = config_model.unmasked_config
        return self

    @cached_property
    def settings_model(self) -> TemplateVersionSettings:
        template = Template.get_template(self.technology_id)
        version = template.versions[self.version_id]
        return version.settings_model.model_validate(self.settings)

    @model_validator(mode="after")
    def validate_settings(self):
        # We validate and dump again to set default settings
        self.settings = self.settings_model.model_dump(mode="json")
        return self


class InternalIntegrationCreate(IntegrationPost):
    pass


class InternalIntegrationUpdate(IntegrationPost):
    pass


class InternalIntegration(IntegrationBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID = Field(frozen=True)
    account_id: UUID = Field(frozen=True)
    category_id: str = Field(frozen=True)
    category_name: str = Field(frozen=True)
    technology_name: str = Field(frozen=True)
    config: dict
    settings: dict
    enabled: bool
    enabled_actions: list = Field(default_factory=list)
    last_activity_at: Optional[datetime] = Field(frozen=True)
    # FIXME: migration_id=connection_id
    # connection_id will be required in this API after migration and config will be removed
    connection_id: Optional[UUID] = Field(frozen=True, default=None)

    @field_validator("config", mode="before")
    def validate_config(cls, config, info):
        template = Template.get_template(info.data["technology_id"])
        version = template.versions[info.data["version_id"]]
        # load the config into the model and dump it back out to ensure secrets are masked
        return version.connection_model.config_model.model_validate(config).model_dump(
            mode="json"
        )


class IntegrationInvokeRequest(BaseModel):
    method_args: dict
    action: Optional[str] = None


class IntegrationInvokeResponse(BaseModel):
    invoke_result: Any
