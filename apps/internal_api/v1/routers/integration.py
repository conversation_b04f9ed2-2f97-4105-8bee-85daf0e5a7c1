from typing import Annotated
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi import status as httpstatus
from fastapi.exceptions import HTTPException

from apps.connectors.integrations.actions.action import (
    IntegrationAction,
)
from apps.connectors.integrations.schemas import (
    IntegrationActionPollingContext,
)
from apps.connectors.models import Connector
from apps.internal_api.v1.crud import connector as crud
from apps.internal_api.v1.schemas.base import ListResponse
from apps.internal_api.v1.schemas.integration import (
    IntegrationInvokeRequest,
    IntegrationInvokeResponse,
    InternalIntegration,
    InternalIntegrationCreate,
    InternalIntegrationSummary,
    InternalIntegrationUpdate,
)

router = APIRouter(
    prefix="/integrations",
    tags=["integrations"],
)


def validate_integration(integration_id):
    """Validate that the integration exists."""
    try:
        return Connector.objects.get(id=integration_id)
    except Connector.DoesNotExist:
        raise HTTPException(
            status_code=httpstatus.HTTP_404_NOT_FOUND,
            detail="Integration not found",
        )


@router.get("/{integration_id}", response_model=InternalIntegration)
def get_integration(
    integration: Connector = Depends(validate_integration),
) -> Connector:
    return integration


@router.get("", response_model=ListResponse[InternalIntegrationSummary])
def list_integrations(
    technology_id: Annotated[str, Query] = None,
    enabled_action: Annotated[list[str] | None, Query()] = None,
    organization_id: Annotated[list[UUID] | None, Query()] = None,
):
    return ListResponse.prepare(
        crud.list(
            technology_id=technology_id,
            enabled_actions=enabled_action,
            organization_id=organization_id,
        )
    )


@router.post("")
def create_integration(
    integration: InternalIntegrationCreate,
) -> InternalIntegration:
    return crud.create(integration)


@router.put("/{integration_id}")
def update_integration(
    integration_id: UUID,
    integration: InternalIntegrationUpdate,
) -> InternalIntegration:
    return crud.update(integration_id, integration)


@router.put("/{integration_id}/product_id/{product_id}")
def update_product_id(
    integration_id: UUID,
    product_id: int,
) -> InternalIntegration:
    update = crud.update_product_id(integration_id, product_id)
    return update


@router.delete("/{integration_id}")
def delete_integration(integration_id: UUID) -> InternalIntegration:
    return crud.delete(integration_id)


@router.post("/{integration_id}/methods/{method_name}")
def invoke_integration_method(
    method_name: str,
    payload: IntegrationInvokeRequest,
    connector: Connector = Depends(validate_integration),
) -> IntegrationInvokeResponse:
    integration = connector.get_integration()
    result = integration.invoke(method_name, **payload.method_args)
    connector.update_last_activity_at()
    return IntegrationInvokeResponse(invoke_result=result)


def create_invoke_action_route(
    action_type: str,
    request_type,
    result_type,
    is_generator,
    supports_polling,
):
    """
    Create a route for invoking an action on an integration.
    :param action_type: The action name to create route for
    :param request_type: The request model for the action
    :param result_type: Expected Result model for the action
    :param is_generator: Whether the action is a generator
    :param supports_polling: Whether the action supports a polling endpoint
    """

    if is_generator:
        result_type = ListResponse[result_type]

    def invoke_action(
        action_args: request_type,
        connector: Connector = Depends(validate_integration),
    ) -> result_type:
        if action_type not in connector.enabled_actions:
            raise HTTPException(
                status_code=httpstatus.HTTP_405_METHOD_NOT_ALLOWED,
                detail=f"Action {action_type} is not enabled for this integration",
            )
        integration = connector.get_integration()

        result = integration.invoke_action(action_type, action_args=action_args)
        if is_generator:
            result = ListResponse.prepare(list(result))

        connector.update_last_activity_at()
        return result

    # Override the function name and qualname so that we can trace each invoke action route
    # by action name. Python module (top-level) functions have the same __name__ and __qualname__.
    # We must override these attributes before adding the route to the router.
    # __qualname__ is used for the NR transaction name.
    # __name__ is used
    #          - to create the openapi path summary
    #          - by generate_operation_id (operation_id is used by our SDK generator)
    function_name_override = f"invoke_action_{action_type}"
    invoke_action.__qualname__ = function_name_override
    invoke_action.__name__ = function_name_override

    router.add_api_route(
        path=f"/{{integration_id}}/actions/{action_type}",
        endpoint=invoke_action,
        methods=["POST"],
    )

    if supports_polling:

        def poll_action(
            poll_context: IntegrationActionPollingContext,
            connector: Connector = Depends(validate_integration),
        ) -> result_type:
            integration = connector.get_integration()
            result = integration.poll_action(action_type, poll_context)
            return result

        function_name_override = f"poll_action_{action_type}"
        poll_action.__qualname__ = function_name_override
        poll_action.__name__ = function_name_override

        router.add_api_route(
            path=f"/{{integration_id}}/actions/{action_type}/poll",
            endpoint=poll_action,
            methods=["POST"],
        )


# Create invoke action routes for all actions in INTEGRATION_ACTIONS_METADATA
for action in IntegrationAction.get_all_action_types():
    if metadata := action.metadata:
        create_invoke_action_route(
            action.action_type,
            metadata.args_type,
            metadata.result_type,
            metadata.is_generator,
            metadata.supports_polling,
        )
