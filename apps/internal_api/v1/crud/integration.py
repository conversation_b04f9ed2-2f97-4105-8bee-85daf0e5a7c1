from typing import Any
from uuid import UUID

from apps.accounts.models import Organization
from apps.api.v1.schemas.integration import IntegrationCreate, IntegrationUpdate
from apps.connectors.models import Connection, Connector

from .base import InternalCRUDBase


class InternalCRUDConnector(
    InternalCRUDBase[Connector, IntegrationCreate, IntegrationUpdate]
):
    def list(
        self,
        organization_id: list[UUID] = None,
        technology_id: str = None,
        enabled_actions: list[str] = None,
    ) -> list[Connector]:
        params = {}
        if organization_id:  # return global objects
            params = {"organization_id__in": organization_id}
        if technology_id:
            params["technology_id"] = technology_id

        result = (
            super()
            .list(params)
            .exclude(organization__sync_status=Organization.SyncStatus.ORPHANED)
            .filter(is_deleted=False)
        )

        if enabled_actions:
            result = [
                r
                for r in result
                if set(enabled_actions).intersection(r.enabled_actions)
            ]

        return result

    def create(self, obj_in: IntegrationCreate) -> Connector:
        if obj_in.connection_id:
            connection = Connection.objects.get(id=obj_in.connection_id)
            # FIXME: migration_id=connection_config
            obj_in.config = connection.config

        obj_in.validate_config()
        obj_in.config_model.validate_database()

        return super().create(obj_in)

    def update(self, id: Any, obj_in: IntegrationUpdate) -> Connector:
        if obj_in.connection_id:
            connection = Connection.objects.get(id=obj_in.connection_id)
            # FIXME: migration_id=connection_config
            obj_in.config = connection.config

        obj_in.validate_config()
        obj_in.config_model.validate_database()

        return super().update(id, obj_in)

    def delete(self, id: Any) -> Connector:
        obj = self.get(id)
        obj.is_deleted = True
        obj.save()
        return obj

    def update_product_id(
        self,
        id: Any,
        product_id: int,
    ) -> Connector:
        """
        Updates only the product_id of a Connector.
        """
        obj = self.get(id)
        obj.product_id = product_id
        obj.save(update_fields={"product_id", "updated_at"})
        return obj


connector = InternalCRUDConnector(Connector)
